<!--Menggunakan template adminlte-->
@extends('adminlte::page')

<!--Mendefinisikan judul halaman-->
@section('title', 'Tambah Artikel')

<!--Mendefinisikan header konten-->
@section('content_header')
    <h1>Tambah Artikel</h1>
@stop

<!--Mendefinisikan konten utama-->
@section('content')
<div class="card">
    <div class="card-body">
        <!-- Form untuk menambah artikel dengan dukungan upload file -->
        <form action="{{ route('admin.website.artikel.store') }}" method="POST" enctype="multipart/form-data">
            @csrf  <!-- Token CSRF untuk keamanan form -->
            
            <!-- Input judul artikel -->
            <div class="form-group">
                <label for="title">Judul</label>
                <input type="text" 
                       class="form-control @error('title') is-invalid @enderror"  
                       id="title" 
                       name="title" 
                       value="{{ old('title') }}"  
                       required>
                @error('title')
                    <div class="invalid-feedback">{{ $message }}</div>  <!-- Menampilkan pesan error -->
                @enderror
            </div>

            <!-- Input penulis artikel -->
            <div class="form-group">
                <label for="writer">Penulis</label>
                <input type="text" 
                       class="form-control @error('writer') is-invalid @enderror"  
                       id="writer" 
                       name="writer" 
                       value="{{ old('writer') }}"  
                       placeholder="Masukkan nama penulis artikel"
                       required>
                @error('writer')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Dropdown unit hanya untuk Administrator -->
            @role('Administrator')
            <div class="form-group">
                <label for="unit_id">Unit</label>
                <select class="form-control @error('unit_id') is-invalid @enderror" 
                        id="unit_id" 
                        name="unit_id" 
                        required>
                    <option value="">Pilih Unit</option>
                    <option value="0" @selected(old('unit_id') == '0')>
                        Semua Unit (Artikel akan muncul di semua jenjang)
                    </option>
                    @foreach($units as $unit)
                        <option value="{{ $unit->id }}" @selected(old('unit_id') == $unit->id)>
                            {{ $unit->nama_unit }}
                        </option>
                    @endforeach
                </select>
                @error('unit_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            @endrole

            <!-- Input ringkasan artikel (opsional) -->
            <div class="form-group">
                <label for="excerpt">Ringkasan (opsional)</label>
                <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                          id="excerpt" 
                          name="excerpt" 
                          rows="3">{{ old('excerpt') }}</textarea>
                @error('excerpt')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Input konten artikel utama -->
            <div class="form-group">
                <label for="content">Konten</label>
                <textarea class="form-control @error('content') is-invalid @enderror" 
                          id="content" 
                          name="content" 
                          rows="10">{{ old('content') }}</textarea>
                @error('content')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Input upload gambar -->
            <div class="form-group">
                <label for="image">Gambar</label>
                <input type="file" 
                       class="form-control-file @error('image') is-invalid @enderror" 
                       id="image" 
                       name="image" 
                       accept="image/*"  
                       required>
                @error('image')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Dropdown status artikel -->
            <div class="form-group">
                <label for="status">Status</label>
                <select class="form-control @error('status') is-invalid @enderror" 
                        id="status" 
                        name="status" 
                        required>
                    <option value="draft" {{ old('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                    <option value="published" {{ old('status') === 'published' ? 'selected' : '' }}>Published</option>
                    <option value="archived" {{ old('status') === 'archived' ? 'selected' : '' }}>Archived</option>
                </select>
                @error('status')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Setelah field status, tambahkan field tanggal publikasi -->
            <div class="form-group">
                <label for="published_at">Tanggal Publikasi</label>
                <input type="date" 
                       class="form-control @error('published_at') is-invalid @enderror" 
                       id="published_at" 
                       name="published_at" 
                       value="{{ old('published_at') }}">
                <small class="form-text text-muted">Kosongkan jika ingin menggunakan tanggal saat ini (untuk status Published)</small>
                @error('published_at')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Tombol submit dan batal -->
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Simpan</button>
                <a href="{{ route('admin.website.artikel.index') }}" class="btn btn-secondary">Batal</a>
            </div>
        </form>
    </div>
</div>
@stop

<!-- Mendefinisikan CSS tambahan -->
@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

<!-- Mendefinisikan JavaScript tambahan -->
@section('js')
    <!-- Load TinyMCE -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/7.6.0/tinymce.min.js"></script>
    <script>
        tinymce.init({
            selector: '#content',
            height: 500,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | ' +
                'bold italic forecolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }',
            images_upload_url: '/upload/image', // Adjust this to your image upload endpoint
            automatic_uploads: true,
            images_reuse_filename: true,
            relative_urls: false,
            remove_script_host: false,
            convert_urls: true
        });
    </script>
@stop

