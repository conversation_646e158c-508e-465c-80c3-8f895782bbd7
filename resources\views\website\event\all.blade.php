
@extends('layouts.website')

@section('title', 'Semua Event')

@section('content')

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">


@php
/**
 * Mengekstrak ID video dari URL YouTube
 *
 * @param string $url URL YouTube
 * @return string|null ID video YouTube atau null jika tidak valid
 */
function getYoutubeVideoId($url) {
    if (empty($url) || !is_string($url)) return null;

    // Sanitize URL
    $url = trim($url);
    if (strlen($url) < 10) return null;

    $pattern =
        '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';

    if (preg_match($pattern, $url, $match)) {
        // Validate video ID format
        if (strlen($match[1]) === 11 && preg_match('/^[a-zA-Z0-9_-]+$/', $match[1])) {
            return $match[1];
        }
    }

    return null;
}
@endphp

<style>
/* ===== STYLING UNTUK HALAMAN EVENT ===== */

/* Reset untuk memastikan styling diterapkan */
* {
    box-sizing: border-box;
}

/* Styling khusus untuk halaman event */
body .event-container h1.event-page-title,
.event-container h1.event-page-title,
h1.event-page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 700 !important;
    font-size: 2.8rem !important;
    text-align: center !important;
    margin-bottom: 3rem !important;
    position: relative !important;
    letter-spacing: -0.5px !important;
    line-height: 1.2 !important;
    display: block !important;
}

/* Container utama dengan background gradient */
.event-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    min-height: 100vh !important;
    padding: 2rem 0 !important;
}

/* ===== STYLING UNTUK JUDUL HALAMAN EVENT ===== */
.event-page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-weight: 800 !important;
    font-size: 3.2rem !important;
    text-align: center !important;
    margin-bottom: 4rem !important;
    position: relative !important;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    letter-spacing: -1px !important;
    line-height: 1.1 !important;
    display: block !important;
    text-transform: uppercase !important;
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Efek underline animasi untuk judul */
body .event-container h1.event-page-title::after,
.event-container h1.event-page-title::after,
h1.event-page-title::after {
    content: '' !important;
    position: absolute !important;
    bottom: -20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 0 !important;
    height: 5px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 3px !important;
    animation: expandLine 2.5s ease-out forwards !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
}

/* Animasi untuk garis bawah judul */
@keyframes expandLine {
    0% {
        width: 0;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        width: 150px;
        opacity: 1;
    }
}

/* Efek shimmer pada judul */
body .event-container h1.event-page-title::before,
.event-container h1.event-page-title::before,
h1.event-page-title::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
    animation: shimmer 4s infinite !important;
    z-index: 1 !important;
}

@keyframes shimmer {
    0% {
        left: -100%;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

/* Efek fade in untuk judul halaman */
.event-page-title {
    animation: fadeInUp 1s ease-out !important;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effect untuk judul */
.event-page-title:hover {
    transform: scale(1.05) !important;
    transition: transform 0.4s ease, filter 0.4s ease !important;
    filter: brightness(1.1) !important;
    cursor: default !important;
}

/* Styling untuk judul individual event di card */
.event-title {
    font-weight: 700 !important;
    font-size: 1.4rem !important;
    color: #2c3e50 !important;
    margin-bottom: 1rem !important;
    line-height: 1.3 !important;
    letter-spacing: -0.3px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    position: relative !important;
    display: block !important;
}

/* Hover effect untuk judul event */
.event-title:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    transform: translateY(-2px) !important;
    transition: all 0.3s ease !important;
}

/* Efek glow untuk judul event saat hover */
.event-card:hover .event-title {
    text-shadow: 0 0 20px rgba(102, 126, 234, 0.3) !important;
}

/* Styling untuk card event */
.event-card {
    background: white;
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.event-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.event-card:hover::before {
    transform: scaleX(1);
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Container media untuk gambar dan video */
.media-container {
    width: 100%;
    height: 220px;
    overflow: hidden;
    position: relative;
    border-radius: 20px 20px 0 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Styling untuk gambar */
.event-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
    border-radius: 0;
}

.event-card:hover .event-image {
    transform: scale(1.05);
}

/* Styling untuk embed YouTube - Bootstrap 5 */
.ratio {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
}

.ratio iframe {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: 0 !important;
    border-radius: 20px 20px 0 0 !important;
}

/* Memastikan iframe YouTube memenuhi container */
.media-container iframe,
.media-container .embed-responsive iframe,
iframe.embed-responsive-item {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    border: none !important;
    border-radius: 20px 20px 0 0 !important;
}

/* Styling untuk body card */
.event-card-body {
    padding: 1.5rem;
    background: white;
}

.event-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
    line-height: 1.4;
}

.event-card:hover .event-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    transform: translateY(-2px) !important;
    text-shadow: 0 0 20px rgba(102, 126, 234, 0.3) !important;
}

/* Styling untuk informasi event */
.event-info {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    color: #6c757d;
    display: flex;
    align-items: center;
}

.event-info i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
    color: #667eea;
}

.event-description {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Styling untuk placeholder jika tidak ada media */
.no-media-placeholder {
    height: 220px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 3rem;
    border-radius: 20px 20px 0 0;
}

/* Styling untuk empty state */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

/* Pagination Controls Styling */
.form-select-sm {
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select-sm:focus {
    border-color: #667eea;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Styling untuk pagination */
.pagination-container {
    margin-top: 0.75rem;
}

.pagination .page-link {
    border: 1px solid #dee2e6;
    color: #667eea;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

    
/* Responsive design */
/* Responsive design untuk judul */
@media (max-width: 768px) {
    .event-page-title {
        font-size: 2.5rem !important;
        margin-bottom: 3rem !important;
        letter-spacing: -0.5px !important;
    }
    
    .event-page-title::after {
        width: 100px !important;
        height: 4px !important;
        bottom: -15px !important;
    }
    
    .event-title {
        font-size: 1.2rem !important;
        line-height: 1.4 !important;
    }
}

@media (max-width: 480px) {
    .event-page-title {
        font-size: 2rem !important;
        margin-bottom: 2.5rem !important;
    }
    
    .event-page-title::after {
        width: 80px !important;
        height: 3px !important;
    }
    
    .event-title {
        font-size: 1.1rem !important;
    }
}
</style>


<div class="event-container">
    <div class="container">
        <h1 class="event-page-title">Semua Event</h1>


        @if($events->count() > 0)
            <div class="row">
                @foreach($events as $event)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card event-card">
                        <div class="media-container">
                            @if($event->gambar)
                                <img src="{{ asset('storage/events/' . $event->gambar) }}"
                                     class="event-image"
                                     alt="{{ $event->judul }}">
                            @elseif($event->youtube_url && getYoutubeVideoId($event->youtube_url))
                                <div class="ratio ratio-16x9">
                                    <iframe src="https://www.youtube.com/embed/{{ getYoutubeVideoId($event->youtube_url) }}?rel=0&modestbranding=1"
                                            title="{{ $event->judul }}"
                                            frameborder="0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            allowfullscreen></iframe>
                                </div>
                            @else
                                <div class="no-media-placeholder">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                            @endif
                        </div>
                        <div class="card-body event-card-body">
                            <h5 class="card-title event-title">{{ $event->judul }}</h5>
                            <div class="event-info">
                                <i class="fas fa-calendar"></i>
                                {{ \Carbon\Carbon::parse($event->tanggal)->format('d M Y') }}
                            </div>
                            <div class="event-info">
                                <i class="fas fa-map-marker-alt"></i>
                                {{ $event->lokasi }}
                            </div>
                            <p class="card-text event-description">{{ Str::limit($event->deskripsi, 100) }}</p>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-calendar-alt"></i>
                <h3>Belum Ada Event</h3>
                <p>Belum ada event yang tersedia saat ini.</p>
            </div>
        @endif

        <!-- Pagination Controls -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="mb-2">
                        <span class="text-muted">
                            @if(isset($perPage) && $perPage === 'all')
                                Menampilkan semua {{ $events->total() }} event
                            @else
                                Menampilkan {{ $events->firstItem() ?? 0 }} - {{ $events->lastItem() ?? 0 }} dari {{ $events->total() }} event
                            @endif
                        </span>
                    </div>
                    <div class="mb-2">
                        <form method="GET" action="{{ route('website.event.all') }}" class="d-inline-flex align-items-center">
                            <label for="per_page" class="me-2 text-muted">Tampilkan:</label>
                            <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                                <option value="9" {{ (isset($perPage) && $perPage == 9) ? 'selected' : '' }}>9</option>
                                <option value="20" {{ (isset($perPage) && $perPage == 20) ? 'selected' : '' }}>20</option>
                                <option value="50" {{ (isset($perPage) && $perPage == 50) ? 'selected' : '' }}>50</option>
                                <option value="100" {{ (isset($perPage) && $perPage == 100) ? 'selected' : '' }}>100</option>
                                <option value="all" {{ (isset($perPage) && $perPage === 'all') ? 'selected' : '' }}>Semua</option>
                            </select>
                            <span class="ms-2 text-muted">per halaman</span>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        @if(isset($perPage) && $perPage !== 'all' && $events->hasPages())
        <div class="pagination-container d-flex justify-content-center mt-4">
            <nav aria-label="Event pagination">
                {{ $events->appends(['per_page' => $perPage])->links('pagination::bootstrap-4') }}
            </nav>
        </div>
        @endif
    </div>
</div>
@endsection


