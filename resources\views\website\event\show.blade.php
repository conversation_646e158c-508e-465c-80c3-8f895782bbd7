@extends('layouts.website')

@section('title', $event->judul)

@section('meta')
<meta name="description" content="{{ Str::limit(strip_tags($event->deskripsi), 160) }}">
<meta name="keywords" content="event, {{ $event->judul }}, sekolah">

<!-- Open Graph Meta Tags -->
<meta property="og:type" content="article">
<meta property="og:title" content="{{ $event->judul }}">
<meta property="og:description" content="{{ Str::limit(strip_tags($event->deskripsi), 160) }}">
@if($event->gambar)
<meta property="og:image" content="{{ asset('storage/' . $event->gambar) }}">
@endif
<meta property="og:url" content="{{ route('website.event.show', $event->slug) }}">

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $event->judul }}">
<meta name="twitter:description" content="{{ Str::limit(strip_tags($event->deskripsi), 160) }}">
@if($event->gambar)
<meta name="twitter:image" content="{{ asset('storage/' . $event->gambar) }}">
@endif
@endsection

@section('css')
<style>
    .event-detail-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .event-header {
        background: white;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .event-image-container {
        position: relative;
        height: 400px;
        overflow: hidden;
    }

    .event-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .event-image:hover {
        transform: scale(1.05);
    }

    .event-date-badge {
        position: absolute;
        top: 20px;
        left: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px 15px;
        border-radius: 15px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .event-content {
        padding: 2rem;
    }

    .event-title {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .event-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 15px;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
        font-size: 0.95rem;
    }

    .meta-item i {
        color: #667eea;
        font-size: 1.1rem;
    }

    .event-description {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #495057;
        text-align: justify;
    }

    .event-description p {
        margin-bottom: 1.5rem;
    }

    .youtube-container {
        margin: 2rem 0;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .related-events {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .related-events h3 {
        color: #2c3e50;
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .related-event-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }

    .related-event-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .related-event-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .related-event-content {
        padding: 1.5rem;
    }

    .related-event-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-decoration: none;
        line-height: 1.3;
    }

    .related-event-title:hover {
        color: #667eea;
        text-decoration: none;
    }

    .related-event-date {
        color: #6c757d;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .breadcrumb {
        background: transparent;
        padding: 1rem 0;
    }

    .breadcrumb-item a {
        color: #667eea;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        text-decoration: underline;
    }

    .breadcrumb-item.active {
        color: #6c757d;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .event-title {
            font-size: 2rem;
        }

        .event-meta {
            flex-direction: column;
            gap: 1rem;
        }

        .event-content {
            padding: 1.5rem;
        }

        .related-events {
            padding: 1.5rem;
        }
    }

    /* YouTube Video Responsive */
    .video-responsive {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 */
    }

    .video-responsive iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 15px;
    }
</style>
@endsection

@section('content')
@php
/**
 * Mengekstrak ID video dari URL YouTube
 */
function getYoutubeVideoId($url) {
    if (empty($url) || !is_string($url)) return null;

    $url = trim($url);
    if (strlen($url) < 10) return null;

    $pattern = '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';

    if (preg_match($pattern, $url, $match)) {
        if (strlen($match[1]) === 11 && preg_match('/^[a-zA-Z0-9_-]+$/', $match[1])) {
            return $match[1];
        }
    }

    return null;
}
@endphp

<div class="event-detail-container">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('website.home') }}">Beranda</a></li>
                <li class="breadcrumb-item"><a href="{{ route('website.event.all') }}">Event</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ Str::limit($event->judul, 50) }}</li>
            </ol>
        </nav>

        <div class="row">
            <div class="col-lg-8">
                <!-- Event Header -->
                <article class="event-header">
                    <div class="event-image-container">
                        @if($event->gambar)
                            <img src="{{ asset('storage/' . $event->gambar) }}" 
                                 alt="{{ $event->judul }}" 
                                 class="event-image">
                        @elseif($event->youtube_url && getYoutubeVideoId($event->youtube_url))
                            <div class="video-responsive">
                                <iframe src="https://www.youtube.com/embed/{{ getYoutubeVideoId($event->youtube_url) }}"
                                        allowfullscreen>
                                </iframe>
                            </div>
                        @else
                            <div class="event-image" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-calendar-alt" style="font-size: 4rem; color: white; opacity: 0.7;"></i>
                            </div>
                        @endif
                        
                        <div class="event-date-badge">
                            <i class="fas fa-calendar-alt"></i>
                            {{ \Carbon\Carbon::parse($event->tanggal)->format('d M Y') }}
                        </div>
                    </div>

                    <div class="event-content">
                        <h1 class="event-title">{{ $event->judul }}</h1>

                        <div class="event-meta">
                            <div class="meta-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>{{ \Carbon\Carbon::parse($event->tanggal)->format('d F Y') }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ $event->lokasi ?: 'Lokasi akan diinformasikan kemudian' }}</span>
                            </div>
                            @if($event->unit)
                            <div class="meta-item">
                                <i class="fas fa-building"></i>
                                <span>{{ $event->unit->nama }}</span>
                            </div>
                            @endif
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span>{{ \Carbon\Carbon::parse($event->tanggal)->diffForHumans() }}</span>
                            </div>
                        </div>

                        <div class="event-description">
                            {!! nl2br(e($event->deskripsi)) !!}
                        </div>

                        @if($event->youtube_url && getYoutubeVideoId($event->youtube_url) && $event->gambar)
                        <div class="youtube-container">
                            <h4 class="mb-3"><i class="fab fa-youtube text-danger"></i> Video Event</h4>
                            <div class="video-responsive">
                                <iframe src="https://www.youtube.com/embed/{{ getYoutubeVideoId($event->youtube_url) }}"
                                        allowfullscreen>
                                </iframe>
                            </div>
                        </div>
                        @endif
                    </div>
                </article>
            </div>

            <div class="col-lg-4">
                <!-- Related Events -->
                @if($relatedEvents->count() > 0)
                <div class="related-events">
                    <h3><i class="fas fa-calendar-alt"></i> Event Lainnya</h3>
                    @foreach($relatedEvents as $relatedEvent)
                    <div class="related-event-card">
                        @if($relatedEvent->gambar)
                            <img src="{{ asset('storage/' . $relatedEvent->gambar) }}" 
                                 alt="{{ $relatedEvent->judul }}" 
                                 class="related-event-image">
                        @elseif($relatedEvent->youtube_url && getYoutubeVideoId($relatedEvent->youtube_url))
                            <div class="related-event-image" style="background-image: url('https://img.youtube.com/vi/{{ getYoutubeVideoId($relatedEvent->youtube_url) }}/maxresdefault.jpg'); background-size: cover; background-position: center;"></div>
                        @else
                            <div class="related-event-image" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-calendar-alt" style="font-size: 2rem; color: white; opacity: 0.7;"></i>
                            </div>
                        @endif
                        
                        <div class="related-event-content">
                            <a href="{{ route('website.event.show', $relatedEvent->slug) }}" class="related-event-title">
                                {{ Str::limit($relatedEvent->judul, 60) }}
                            </a>
                            <div class="related-event-date">
                                <i class="fas fa-calendar-alt"></i>
                                {{ \Carbon\Carbon::parse($relatedEvent->tanggal)->format('d M Y') }}
                            </div>
                        </div>
                    </div>
                    @endforeach

                    <div class="text-center mt-3">
                        <a href="{{ route('website.event.all') }}" class="btn btn-outline-primary">
                            <i class="fas fa-eye"></i> Lihat Semua Event
                        </a>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll untuk internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Lazy loading untuk gambar
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
});
</script>
@endsection
