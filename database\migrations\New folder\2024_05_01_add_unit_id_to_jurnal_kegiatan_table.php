<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('jurnal_kegiatan', function (Blueprint $table) {
            $table->foreignId('unit_id')
                  ->nullable()
                  ->after('user_id')
                  ->constrained('units')
                  ->onDelete('set null');
        });
        
        // Mengisi nilai unit_id berdasarkan unit_id user untuk data yang sudah ada
        DB::statement('UPDATE jurnal_kegiatan jk JOIN users u ON jk.user_id = u.id SET jk.unit_id = u.unit_id WHERE jk.unit_id IS NULL');
    }

    public function down(): void
    {
        Schema::table('jurnal_kegiatan', function (Blueprint $table) {
            $table->dropForeign(['unit_id']);
            $table->dropColumn('unit_id');
        });
    }
};