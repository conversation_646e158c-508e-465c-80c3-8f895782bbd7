<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-body">
        <form action="<?php echo e(route('admin.website.event.update', $event->id)); ?>" method="POST" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div class="form-group">
                <label for="judul">Judul Event</label>
                <input type="text" class="form-control <?php $__errorArgs = ['judul'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="judul" name="judul" value="<?php echo e(old('judul', $event->judul)); ?>" required>
                <?php $__errorArgs = ['judul'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Administrator')): ?>
            <div class="form-group">
                <label for="unit_id">Unit</label>
                <select class="form-control <?php $__errorArgs = ['unit_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        id="unit_id" name="unit_id" required>
                    <option value="">Pilih Unit</option>
                    <?php $__currentLoopData = $units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($unit->id); ?>" 
                            <?php echo e(old('unit_id', $event->unit_id) == $unit->id ? 'selected' : ''); ?>>
                            <?php echo e($unit->nama_unit); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php $__errorArgs = ['unit_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            <?php else: ?>
                <input type="hidden" name="unit_id" value="<?php echo e(auth()->user()->unit_id); ?>">
            <?php endif; ?>

            <!-- Field lainnya -->
            <div class="form-group">
                <label for="tanggal">Tanggal Event</label>
                <input type="date" class="form-control <?php $__errorArgs = ['tanggal'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="tanggal" name="tanggal" value="<?php echo e(old('tanggal', $event->tanggal)); ?>" required>
            </div>

            <div class="form-group">
                <label for="lokasi">Lokasi</label>
                <input type="text" class="form-control <?php $__errorArgs = ['lokasi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="lokasi" name="lokasi" value="<?php echo e(old('lokasi', $event->lokasi)); ?>" required>
            </div>

            <div class="form-group">
                <label for="deskripsi">Deskripsi</label>
                <textarea class="form-control <?php $__errorArgs = ['deskripsi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                          id="deskripsi" name="deskripsi" rows="5" required><?php echo e(old('deskripsi', $event->deskripsi)); ?></textarea>
                <?php $__errorArgs = ['deskripsi'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="form-group">
                <label>Jenis Media</label>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" id="jenis_media_gambar" name="jenis_media" class="custom-control-input" value="gambar" <?php echo e($event->youtube_url ? '' : 'checked'); ?>>
                    <label class="custom-control-label" for="jenis_media_gambar">Gambar</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" id="jenis_media_youtube" name="jenis_media" class="custom-control-input" value="youtube" <?php echo e($event->youtube_url ? 'checked' : ''); ?>>
                    <label class="custom-control-label" for="jenis_media_youtube">Video YouTube</label>
                </div>
            </div>

            <div class="form-group" id="gambar-container">
                <label for="gambar">Gambar Event</label>
                <?php if($event->gambar): ?>
                    <div class="mb-2">
                        <img src="<?php echo e(asset('storage/events/'.$event->gambar)); ?>" alt="<?php echo e($event->judul); ?>" class="img-thumbnail" width="200">
                    </div>
                <?php endif; ?>
                <input type="file" class="form-control-file <?php $__errorArgs = ['gambar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="gambar" name="gambar" accept="image/jpeg,image/png,image/jpg">
                <small class="form-text text-muted">Format: JPEG, PNG, JPG. Maksimal 2MB. Biarkan kosong jika tidak ingin mengubah gambar.</small>
                <?php $__errorArgs = ['gambar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="form-group" id="youtube-container" style="display: none;">
                <label for="youtube_url">Link YouTube</label>
                <input type="text" class="form-control <?php $__errorArgs = ['youtube_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="youtube_url" name="youtube_url" value="<?php echo e(old('youtube_url', $event->youtube_url)); ?>" 
                       placeholder="Contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX">
                <small class="form-text text-muted">Masukkan URL video YouTube.</small>
                <?php $__errorArgs = ['youtube_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <?php if($event->youtube_url): ?>
                    <div class="mt-2">
                        <p><strong>Video URL saat ini:</strong> <?php echo e($event->youtube_url); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <button type="submit" class="btn btn-primary">Update</button>
        </form>
    </div>
</div>
<a href="<?php echo e(route('admin.website.event.index')); ?>" class="btn btn-secondary">Batal</a>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script>
    // Fungsi untuk menampilkan form sesuai jenis media yang dipilih
    $(document).ready(function() {
        // Set kondisi awal berdasarkan data yang ada
        if ($('#jenis_media_youtube').is(':checked')) {
            $('#gambar-container').hide();
            $('#youtube-container').show();
        } else {
            $('#gambar-container').show();
            $('#youtube-container').hide();
        }
        
        // Menangani perubahan pada radio button jenis media
        $('input[name="jenis_media"]').change(function() {
            if ($(this).val() === 'gambar') {
                $('#gambar-container').show();
                $('#youtube-container').hide();
            } else {
                $('#gambar-container').hide();
                $('#youtube-container').show();
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>





<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/website/event/edit.blade.php ENDPATH**/ ?>