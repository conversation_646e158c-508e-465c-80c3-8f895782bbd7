<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('jurnal_kegiatan', function (Blueprint $table) {
            $table->decimal('jam_mengajar', 5, 2)->default(0)->after('keterangan');
            $table->decimal('jam_tambahan', 5, 2)->default(0)->after('jam_mengajar');
            $table->text('detail_jam_mengajar')->nullable()->after('jam_tambahan');
            $table->text('detail_jam_tambahan')->nullable()->after('detail_jam_mengajar');
        });
    }

    public function down(): void
    {
        Schema::table('jurnal_kegiatan', function (Blueprint $table) {
            $table->dropColumn(['jam_mengajar', 'jam_tambahan', 'detail_jam_mengajar', 'detail_jam_tambahan']);
        });
    }
};