@extends('layouts.website')

@section('title', 'Artikel')

@section('content')
<div class="container py-5">
    <div class="title-container mb-5">
        <div class="text-center">
            <h1 class="artikel-title">Artikel Terbaru</h1>
            <div class="title-decoration"></div>
        </div>
    </div>
    
    <div class="row">
        @foreach($articles as $article)
        <div class="col-md-4 mb-4">
            <div class="card artikel-card h-100">
                <div class="artikel-image">
                    <img src="{{ Storage::url($article->image) }}" class="card-img-top" alt="{{ $article->title }}">
                    <div class="artikel-overlay">
                        <a href="{{ route('website.artikel.show', $article->slug) }}" class="btn btn-light btn-sm">
                            <i class="fas fa-eye"></i> Lihat Detail
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ $article->title }}</h5>
                    <p class="text-muted">
                        <i class="fas fa-calendar-alt"></i> {{ $article->created_at->format('d M Y') }}
                    </p>
                    <p class="card-text">{{ Str::limit($article->excerpt, 100) }}</p>
                    <a href="{{ route('website.artikel.show', $article->slug) }}" class="btn btn-primary btn-sm">
                        Baca Selengkapnya
                    </a>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination Controls -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="mb-2">
                    <span class="text-muted">
                        @if(isset($perPage) && $perPage === 'all')
                            Menampilkan semua {{ $articles->total() }} artikel
                        @else
                            Menampilkan {{ $articles->firstItem() ?? 0 }} - {{ $articles->lastItem() ?? 0 }} dari {{ $articles->total() }} artikel
                        @endif
                    </span>
                </div>
                <div class="mb-2">
                    <form method="GET" action="{{ route('website.artikel') }}" class="d-inline-flex align-items-center">
                        <label for="per_page" class="me-2 text-muted">Tampilkan:</label>
                        <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                            <option value="9" {{ (isset($perPage) && $perPage == 9) ? 'selected' : '' }}>9</option>
                            <option value="20" {{ (isset($perPage) && $perPage == 20) ? 'selected' : '' }}>20</option>
                            <option value="50" {{ (isset($perPage) && $perPage == 50) ? 'selected' : '' }}>50</option>
                            <option value="100" {{ (isset($perPage) && $perPage == 100) ? 'selected' : '' }}>100</option>
                            <option value="all" {{ (isset($perPage) && $perPage === 'all') ? 'selected' : '' }}>Semua</option>
                        </select>
                        <span class="ms-2 text-muted">per halaman</span>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    @if(isset($perPage) && $perPage !== 'all' && $articles->hasPages())
    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="Artikel pagination">
            {{ $articles->appends(['per_page' => $perPage])->links('pagination::bootstrap-4') }}
        </nav>
    </div>
    @endif
</div>

<style>
/* Styling untuk judul halaman */
.title-container {
    position: relative;
    padding: 20px 0;
    margin-bottom: 50px;
}

.artikel-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.artikel-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #3498db, #2ecc71);
    border-radius: 2px;
}

.title-decoration {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 2px;
    background-color: #ecf0f1;
}

.title-container::before,
.title-container::after {
    content: '★';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #3498db;
}

.title-container::before {
    left: 25%;
}

.title-container::after {
    right: 25%;
}

/* Styling untuk kartu artikel */
.artikel-card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.artikel-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.artikel-image {
    position: relative;
    overflow: hidden;
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.artikel-card:hover .card-img-top {
    transform: scale(1.1);
}

.artikel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.artikel-card:hover .artikel-overlay {
    opacity: 1;
}

.card-title {
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.card-text {
    color: #7f8c8d;
    margin-bottom: 15px;
}

.text-muted {
    font-size: 0.9rem;
    margin-bottom: 10px;
}

/* Pagination Controls Styling */
.form-select-sm {
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select-sm:focus {
    border-color: #3498db;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Pagination styling */
.pagination {
    margin-top: 2rem;
}

.page-link {
    color: #3498db;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.page-link:hover {
    background-color: #3498db;
    color: #fff;
    border-color: #3498db;
}

.page-item.active .page-link {
    background-color: #3498db;
    border-color: #3498db;
    color: #fff;
}

.page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Responsif untuk layar kecil */
@media (max-width: 768px) {
    .artikel-title {
        font-size: 2rem;
    }

    .title-container::before {
        left: 10%;
    }

    .title-container::after {
        right: 10%;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-inline-flex.align-items-center {
        justify-content: center;
    }
}
</style>
@endsection


