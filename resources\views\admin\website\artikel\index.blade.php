@extends('adminlte::page')

@section('title', 'Manajemen Artikel')

@section('content_header')
    <h1>Manajemen Artikel</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Artikel</h3>
        <div class="card-tools">
            <a href="{{ route('admin.website.artikel.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Artikel
            </a>
        </div>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th style="width: 50px">No</th>
                    <th style="width: 150px">Gambar</th>
                    <th>Judul</th>
                    <th>Status</th>
                    <th>Penulis</th>
                    <th>Tanggal Publikasi</th>
                    <th style="width: 180px">Aksi</th>
                </tr>
            </thead>
            <tbody>
                @forelse($articles as $article)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>
                        <img src="{{ asset('storage/' . $article->image) }}" 
                             alt="{{ $article->title }}" 
                             class="img-thumbnail"
                             style="max-height: 100px">
                    </td>
                    <td>{{ $article->title }}</td>
                    <td>
                        <span class="badge badge-{{ $article->status === 'published' ? 'success' : ($article->status === 'draft' ? 'warning' : 'secondary') }}">
                            {{ ucfirst($article->status) }}
                        </span>
                    </td>
                    <td>{{ $article->author->name }}</td>
                    <td>{{ $article->published_at ? $article->published_at->format('d/m/Y H:i') : '-' }}</td>
                    <td>
                        @if($article->status === 'published')
                            <a href="{{ route('website.artikel.show', $article->slug) }}"
                               class="btn btn-sm btn-success"
                               target="_blank"
                               title="Lihat Artikel">
                                <i class="fas fa-eye"></i>
                            </a>
                        @endif
                        <a href="{{ route('admin.website.artikel.edit', $article) }}"
                           class="btn btn-sm btn-info"
                           title="Edit Artikel">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.website.artikel.destroy', $article) }}"
                              method="POST"
                              class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    class="btn btn-sm btn-danger"
                                    onclick="return confirm('Apakah Anda yakin ingin menghapus artikel ini?')"
                                    title="Hapus Artikel">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="7" class="text-center">Tidak ada artikel</td>
                </tr>
                @endforelse
            </tbody>
        </table>

        <div class="mt-3">
            {{ $articles->links() }}
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Auto close alert after 3 seconds
            setTimeout(function() {
                $(".alert").alert('close');
            }, 3000);
        });
    </script>
@stop
