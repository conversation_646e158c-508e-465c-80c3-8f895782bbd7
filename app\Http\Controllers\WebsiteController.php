<?php

namespace App\Http\Controllers;

use App\Models\Slide;
use App\Models\Article;
use App\Models\Event;
use App\Models\Achievement;
use App\Models\Unit;
use App\Models\Setting;
use App\Models\Ekstrakurikuler;
use Illuminate\Http\Request;
use Spatie\Permission\Traits\HasRoles;

class WebsiteController extends Controller
{
    use HasRoles;

    /**
     * Menampilkan halaman utama website
     * Memuat data slides, artikel terbaru, prestasi terbaru, 
     * ekstrakurikuler dan sambutan kepala sekolah
     */
    public function index()
    {
        $slides = Slide::where('status', true)
                      ->orderBy('order')
                      ->get();
        
        $articles = Article::latest()
                         ->take(3)
                         ->get();
                         
        $achievements = Achievement::latest()
                                 ->take(4)
                                 ->get();
                                 
        $ekstrakurikuler = Ekstrakurikuler::all();
        
        $sambutan = Setting::where('key', 'sambutan_kepsek')
                          ->value('value');

        return view('welcome', compact(
            'slides',
            'articles',
            'achievements',
            'ekstrakurikuler',
            'sambutan'
        ));
    }

    /**
     * Menampilkan daftar artikel yang telah dipublikasikan
     * dengan pagination yang dapat diatur per halaman
     */
    public function artikel(Request $request)
    {
        $perPage = $request->get('per_page', 9);

        // Validasi nilai per_page
        $allowedPerPage = [9, 20, 50, 100, 'all'];
        if (!in_array($perPage, $allowedPerPage)) {
            $perPage = 9;
        }

        // Jika 'all', ambil semua data tanpa pagination
        if ($perPage === 'all') {
            $articles = Article::where('status', 'published')->latest()->get();
            // Buat objek pagination manual untuk konsistensi
            $articles = new \Illuminate\Pagination\LengthAwarePaginator(
                $articles,
                $articles->count(),
                $articles->count(),
                1,
                ['path' => request()->url(), 'pageName' => 'page']
            );
        } else {
            $articles = Article::where('status', 'published')
                              ->latest()
                              ->paginate($perPage);
            $articles->appends(['per_page' => $perPage]);
        }

        return view('website.artikel.index', compact('articles', 'perPage'));
    }

    /**
     * Menampilkan detail artikel berdasarkan slug
     * @param string $slug - URL slug artikel
     */
    public function showArtikel($slug)
    {
        $article = Article::where('slug', $slug)->firstOrFail();
        return view('website.artikel.show', compact('article'));
    }

    /**
     * Menampilkan halaman profil sekolah
     * Data diambil dari tabel settings dengan key 'profil_sekolah'
     */
    public function profil()
    {
        $profile = Setting::where('key', 'profil_sekolah')->first();
        return view('website.halaman.profil', compact('profile'));
    }

    /**
     * Menampilkan halaman visi misi sekolah
     * Data diambil dari tabel settings dengan key 'visi_misi'
     */
    public function visiMisi()
    {
        $visiMisi = Setting::where('key', 'visi_misi')->first();
        return view('website.halaman.visi-misi', compact('visiMisi'));
    }

    /**
     * Menampilkan halaman sejarah sekolah
     * Data diambil dari tabel settings dengan key 'sejarah'
     */
    public function sejarah()
    {
        $sejarah = Setting::where('key', 'sejarah')->first();
        return view('website.halaman.sejarah', compact('sejarah'));
    }


    /**
     * Menampilkan daftar event terbaru public
     * dengan pagination yang dapat diatur per halaman
     */
     public function event(Request $request)
    {
        $perPage = $request->get('per_page', 9);

        // Validasi nilai per_page
        $allowedPerPage = [9, 20, 50, 100, 'all'];
        if (!in_array($perPage, $allowedPerPage)) {
            $perPage = 10;
        }

        // Jika 'all', ambil semua data tanpa pagination
        if ($perPage === 'all') {
            $events = Event::latest()->get();
            // Buat objek pagination manual untuk konsistensi
            $events = new \Illuminate\Pagination\LengthAwarePaginator(
                $events,
                $events->count(),
                $events->count(),
                1,
                ['path' => request()->url(), 'pageName' => 'page']
            );
        } else {
            $events = Event::latest()->paginate($perPage);
            $events->appends(['per_page' => $perPage]);
        }

        return view('website.event', compact('events', 'perPage'));
    }

    /**
     * Menampilkan semua event
     * dengan pagination 9 event per halaman
     */
    
    public function allEvents()
    {
        $events = Event::latest()->paginate(9);
        return view('website.event.all', compact('events'));
    } 

    /**
     * Menampilkan semua prestasi
     * dengan pagination yang dapat diatur per halaman
     */
    public function allPrestasi(Request $request)
    {
        $perPage = $request->get('per_page', 9);

        // Validasi nilai per_page
        $allowedPerPage = [9, 20, 50, 100, 'all'];
        if (!in_array($perPage, $allowedPerPage)) {
            $perPage = 9;
        }

        // Jika 'all', ambil semua data tanpa pagination
        if ($perPage === 'all') {
            $achievements = Achievement::latest()->get();
            // Buat objek pagination manual untuk konsistensi
            $achievements = new \Illuminate\Pagination\LengthAwarePaginator(
                $achievements,
                $achievements->count(),
                $achievements->count(),
                1,
                ['path' => request()->url(), 'pageName' => 'page']
            );
        } else {
            $achievements = Achievement::latest()->paginate($perPage);
            $achievements->appends(['per_page' => $perPage]);
        }

        return view('website.prestasi.all', compact('achievements', 'perPage'));
    }

    /**
     * Menampilkan prestasi berdasarkan jenjang pendidikan
     * @param string $jenjang - Jenjang pendidikan (paud/sd/smp/sma)
     * Memvalidasi jenjang yang diperbolehkan dan menampilkan prestasi
     * yang sesuai dengan unit pendidikan tersebut
     */
    public function prestasiByJenjang($jenjang)
    {
        // Validasi jenjang yang diperbolehkan
        $allowedJenjang = ['paud', 'sd', 'smp', 'sma'];
        if (!in_array(strtolower($jenjang), $allowedJenjang)) {
            return redirect()->route('website.prestasi.all')
                ->with('error', 'Jenjang pendidikan tidak valid.');
        }

        try {
            $unit = Unit::where('jenjang_id', strtoupper($jenjang === 'paud' ? 'pg' : $jenjang))
                ->firstOrFail();

            $achievements = Achievement::with('unit')
                ->where('unit_id', $unit->id)
                ->latest()
                ->paginate(9);

            return view('website.prestasi.jenjang', [
                'achievements' => $achievements,
                'jenjang' => $jenjang,
                'unit' => $unit
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in prestasiByJenjang: ' . $e->getMessage());
            return redirect()->route('website.prestasi.all')
                ->with('error', 'Jenjang pendidikan tidak ditemukan.');
        }
    }

    /**
     * Menampilkan detail prestasi berdasarkan slug
     * @param string $slug - URL slug prestasi
     */
    public function showPrestasi($slug)
    {
        $achievement = Achievement::with('unit')->where('slug', $slug)->firstOrFail();
        return view('website.prestasi.show', compact('achievement'));
    }

    /**
     * Menampilkan daftar ekstrakurikuler berdasarkan jenjang
     * @param string $jenjang - Jenjang pendidikan (paud/sd/smp/sma)
     */
    public function ekstrakurikuler()
    {
        $ekstrakurikulers = Ekstrakurikuler::with('unit')->get()
            ->groupBy(function($item) {
                return $item->unit->nama_unit;
            });
        
        return view('website.ekstrakurikuler.index', compact('ekstrakurikulers'));
    }

    public function ekstrakurikulerByJenjang($jenjang)
    {
        try {
            // Konversi 'paud' ke 'pg' untuk query jika diperlukan
            $jenjangId = strtoupper($jenjang === 'paud' ? 'pg' : $jenjang);
            
            // Ambil unit berdasarkan jenjang
            $unit = Unit::where('jenjang_id', $jenjangId)->firstOrFail();
            
            // Ambil ekstrakurikuler untuk unit tersebut
            $ekstrakurikulers = Ekstrakurikuler::where('unit_id', $unit->id)->get();
            
            return view('website.ekstrakurikuler.jenjang', compact('ekstrakurikulers', 'unit', 'jenjang'));
        } catch (\Exception $e) {
            return redirect()->route('website.home')
                ->with('error', 'Jenjang pendidikan tidak ditemukan.');
        }
    }
}
