<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Jalankan migrasi.
     */
    public function up(): void
    {
        Schema::table('guru_tugastambahan', function (Blueprint $table) {
            $table->string('kelas')->nullable()->after('tugas_tambahan');
        });
    }

    /**
     * Batalkan migrasi.
     */
    public function down(): void
    {
        Schema::table('guru_tugastambahan', function (Blueprint $table) {
            $table->dropColumn('kelas');
        });
    }
};