<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('guru', function (Blueprint $table) {
            $table->id();
            $table->foreignId('unit_id')->constrained('units');
            $table->string('nip')->nullable();
            $table->string('nama');
            $table->string('nik', 16)->unique();
            $table->enum('jenis_kelamin', ['L', 'P']);
            $table->string('tempat_lahir', 100);
            $table->date('tanggal_lahir');
            $table->string('nama_ibu_kandung');
            // Alamat
            $table->text('alamat');
            $table->string('kelurahan', 100);
            $table->string('kecamatan', 100);
            $table->string('kabupaten', 100);
            $table->string('provinsi', 100);
            // Data pribadi
            $table->string('agama', 10);
            $table->string('npwp', 30)->nullable();
            $table->string('nama_wajib_pajak')->nullable();
            $table->string('kewarganegaraan', 50);
            $table->string('status_kawin', 50);
            $table->string('nama_pasangan')->nullable();
            $table->string('pekerjaan_pasangan', 100)->nullable();
            // Kepegawaian
            $table->string('status_pegawai', 50);
            $table->string('niy', 30)->nullable();
            $table->string('nuptk', 30)->nullable();
            $table->string('jenis_ptk', 50);
            $table->string('sk_pengangkatan', 50)->nullable();
            $table->date('tmt_pengangkatan')->nullable();
            $table->string('lembaga_pengangkat', 100)->nullable();
            $table->string('pangkat_golongan', 50)->nullable();
            // Penugasan
            $table->string('sk_penugasan', 50)->nullable();
            $table->date('tmt_penugasan')->nullable();
            $table->string('lembaga_penugasan', 100)->nullable();
            $table->string('pangkat_golongan_penugasan', 50)->nullable();
            // Kontak
            $table->string('no_telp', 15)->nullable();
            $table->string('email', 100)->nullable();
            $table->string('mata_pelajaran', 100)->nullable();
            $table->enum('status', ['Aktif', 'Non-Aktif']);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('guru');
    }
};