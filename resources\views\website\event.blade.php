@extends('layouts.website')

@section('title', 'allEvent')

@section('css')
<style>
    /* Memperbaiki ukuran video YouTube agar sama dengan gambar */
    .card-img-top {
        height: 250px;
        object-fit: cover;
        width: 100%;
        border-radius: 15px 15px 0 0;
    }

    /* Container media untuk memastikan ukuran yang konsisten */
    .media-container {
        width: 100%;
        height: 250px;
        overflow: hidden;
        position: relative;
        border-radius: 15px 15px 0 0;
        background-color: #f8f9fa;
    }

    /* Styling untuk embed YouTube - Bootstrap 5 */
    .ratio {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding: 0 !important;
        margin: 0 !important;
        overflow: hidden;
        border-radius: 15px 15px 0 0;
    }

    .ratio iframe {
        border-radius: 15px 15px 0 0;
    }

    .embed-responsive-16by9 {
        padding-bottom: 0 !important;
        height: 100% !important;
    }

    .embed-responsive-item {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        border: 0 !important;
        border-radius: 15px 15px 0 0 !important;
        object-fit: cover !important;
    }

    /* Memastikan iframe YouTube memenuhi container */
    .media-container iframe,
    .media-container .embed-responsive iframe,
    iframe.embed-responsive-item {
        width: 100% !important;
        height: 100% !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        border: none !important;
        border-radius: 15px 15px 0 0 !important;
        object-fit: cover !important;
        transform: scale(1.2) !important;
        transform-origin: center center !important;
    }

    /* Memastikan card memiliki ukuran yang sama */
    .card {
        height: 100%;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        background: #fff;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    /* Override untuk memastikan tidak ada padding atau margin yang mengganggu */
    .embed-responsive-16by9 {
        padding-bottom: 0 !important;
        height: 100% !important;
    }

    /* Memastikan gambar dan video memiliki ukuran yang sama persis */
    .media-container img,
    .media-container .embed-responsive,
    .media-container iframe {
        width: 100%;
        height: 250px;
        object-fit: cover;
    }

    /* CSS khusus untuk mengatasi masalah ukuran YouTube */
    .media-container .embed-responsive {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100% !important;
        height: 100% !important;
    }

    /* Memaksa iframe YouTube untuk memenuhi container */
    .media-container iframe[src*="youtube.com"],
    .media-container iframe[src*="youtu.be"] {
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        width: 120% !important;
        height: 120% !important;
        transform: translate(-50%, -50%) !important;
        border: none !important;
        border-radius: 15px 15px 0 0 !important;
    }

    /* CSS tambahan untuk memastikan YouTube memenuhi container */
    .media-container {
        position: relative !important;
        overflow: hidden !important;
    }

    .media-container .embed-responsive,
    .media-container .embed-responsive-16by9 {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Solusi alternatif untuk YouTube yang stubborn */
    .media-container iframe {
        min-width: 100% !important;
        min-height: 100% !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
    }
</style>
@endsection

@php
/**
 * Mengekstrak ID video dari URL YouTube
 *
 * @param string $url URL YouTube
 * @return string|null ID video YouTube atau null jika tidak valid
 */
function getYoutubeVideoId($url) {
    if (empty($url) || !is_string($url)) return null;

    // Sanitize URL
    $url = trim($url);
    if (strlen($url) < 10) return null;

    $pattern =
        '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';

    if (preg_match($pattern, $url, $match)) {
        // Validate video ID format
        if (strlen($match[1]) === 11 && preg_match('/^[a-zA-Z0-9_-]+$/', $match[1])) {
            return $match[1];
        }
    }

    return null;
}
@endphp

@section('content')
<div class="container py-5">
    <div class="title-container mb-5">
        <div class="text-center">
            <h1 class="event-title">Event Sekolah</h1>
            <div class="title-decoration"></div>
        </div>
    </div>

    <!-- Pagination Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="mb-2">
                    <span class="text-muted">
                        @if(isset($perPage) && $perPage === 'all')
                            Menampilkan semua {{ $events->total() }} event
                        @else
                            Menampilkan {{ $events->firstItem() ?? 0 }} - {{ $events->lastItem() ?? 0 }} dari {{ $events->total() }} event
                        @endif
                    </span>
                </div>
                <div class="mb-2">
                    <form method="GET" action="{{ route('website.event') }}" class="d-inline-flex align-items-center">
                        <label for="per_page" class="me-2 text-muted">Tampilkan:</label>
                        <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                            <option value="10" {{ (isset($perPage) && $perPage == 10) ? 'selected' : '' }}>10</option>
                            <option value="20" {{ (isset($perPage) && $perPage == 20) ? 'selected' : '' }}>20</option>
                            <option value="50" {{ (isset($perPage) && $perPage == 50) ? 'selected' : '' }}>50</option>
                            <option value="100" {{ (isset($perPage) && $perPage == 100) ? 'selected' : '' }}>100</option>
                            <option value="all" {{ (isset($perPage) && $perPage === 'all') ? 'selected' : '' }}>Semua</option>
                        </select>
                        <span class="ms-2 text-muted">per halaman</span>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        @foreach($events as $event)
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="media-container">
                    @if($event->gambar)
                        <img src="{{ asset('storage/events/' . $event->gambar) }}" 
                             class="card-img-top" 
                             alt="{{ $event->judul }}">
                    @elseif($event->youtube_url && getYoutubeVideoId($event->youtube_url))
                        <div class="ratio ratio-16x9">
                            <iframe src="https://www.youtube.com/embed/{{ getYoutubeVideoId($event->youtube_url) }}?rel=0&modestbranding=1"
                                    title="{{ $event->judul }}"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen></iframe>
                        </div>
                    @endif
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ $event->judul }}</h5>
                    <p class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        {{ \Carbon\Carbon::parse($event->tanggal)->format('d M Y') }}
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt"></i> 
                        {{ $event->lokasi }}
                    </p>
                    <p class="card-text">{{ Str::limit($event->deskripsi, 100) }}</p>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    @if(isset($perPage) && $perPage !== 'all' && $events->hasPages())
    <div class="d-flex justify-content-center mt-5">
        <nav aria-label="Event pagination">
            {{ $events->appends(['per_page' => $perPage])->links('pagination::bootstrap-4') }}
        </nav>
    </div>
    @endif
</div>

<!-- style jangan dihapus -->
<style>
.title-container {
    position: relative;
    padding: 40px 0;
    text-align: center;
    margin-bottom: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.event-title {
    font-size: 2.8rem;
    font-weight: 800;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 3px;
    margin: 0 auto;
    padding-bottom: 20px;
    position: relative;
    display: inline-block;
}

.event-title::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(to right, #e74c3c, #f39c12);
    border-radius: 2px;
}

.event-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 16px;
    background-color: #fff;
    border: 3px solid #e74c3c;
    border-radius: 50%;
}

.title-container::before,
.title-container::after {
    content: '•';
    font-size: 2rem;
    color: #e74c3c;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.title-container::before {
    left: 20%;
}

.title-container::after {
    right: 20%;
}

/* Tambahan dekorasi */
.title-container .text-center::before,
.title-container .text-center::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 100px;
    height: 2px;
    background: linear-gradient(to right, transparent, #e74c3c, transparent);
}

.title-container .text-center::before {
    right: 65%;
}

.title-container .text-center::after {
    left: 65%;
}

/* Card styling */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
    border-radius: 15px 15px 0 0;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.text-muted {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.text-muted i {
    margin-right: 8px;
    color: #e74c3c;
}

.card-text {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Responsive styling */
@media (max-width: 768px) {
    .event-title {
        font-size: 2rem;
    }
    
    .title-container::before {
        left: 5%;
    }
    
    .title-container::after {
        right: 5%;
    }
    
    .title-container .text-center::before,
    .title-container .text-center::after {
        width: 50px;
    }
    
    .title-container .text-center::before {
        right: 75%;
    }
    
    .title-container .text-center::after {
        left: 75%;
    }
}

/* Pagination Controls Styling */
.form-select-sm {
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select-sm:focus {
    border-color: #e74c3c;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
}

/* Pagination styling */
.pagination {
    margin-top: 2rem;
}

.page-link {
    color: #e74c3c;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.page-link:hover {
    background-color: #e74c3c;
    color: #fff;
    border-color: #e74c3c;
}

.page-item.active .page-link {
    background-color: #e74c3c;
    border-color: #e74c3c;
    color: #fff;
}

.page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Responsive pagination controls */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-inline-flex.align-items-center {
        justify-content: center;
    }
}
</style>
@endsection






