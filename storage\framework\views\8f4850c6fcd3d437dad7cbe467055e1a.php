<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Event</h3>
        <div class="card-tools">
            <a href="<?php echo e(route('admin.website.event.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Event
            </a>
        </div>
    </div>
    <div class="card-body">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Judul</th>
                    <th>Tanggal</th>
                    <th>Lokasi</th>
                    <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Administrator')): ?>
                    <th>Unit</th>
                    <?php endif; ?>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($loop->iteration); ?></td>
                    <td><?php echo e($event->judul); ?></td>
                    <td><?php echo e(\Carbon\Carbon::parse($event->tanggal)->format('d/m/Y')); ?></td>
                    <td><?php echo e($event->lokasi); ?></td>
                    <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Administrator')): ?>
                    <td><?php echo e($event->unit->nama_unit ?? '-'); ?></td>
                    <?php endif; ?>
                    <td>
                        <a href="<?php echo e(route('admin.website.event.edit', $event->id)); ?>" 
                           class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="<?php echo e(route('admin.website.event.destroy', $event->id)); ?>" 
                              method="POST" class="d-inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-sm btn-danger" 
                                    onclick="return confirm('Yakin ingin menghapus?')">
                                Hapus
                            </button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
        <?php echo e($events->links()); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>



<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/website/event/index.blade.php ENDPATH**/ ?>