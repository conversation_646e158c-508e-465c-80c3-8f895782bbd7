<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Guru;
use App\Models\TugasTambahan;

return new class extends Migration
{
    /**
     * Jalankan migrasi.
     */
    public function up(): void
    {
        Schema::table('guru_tugastambahan', function (Blueprint $table) {
            $table->string('nama_guru')->nullable()->after('guru_id');
        });

        // Isi kolom nama_guru untuk data yang sudah ada
        $tugasTambahan = TugasTambahan::all();
        foreach ($tugasTambahan as $tugas) {
            $guru = Guru::find($tugas->guru_id);
            if ($guru) {
                $tugas->nama_guru = $guru->nama;
                $tugas->save();
            }
        }
    }

    /**
     * Batalkan migrasi.
     */
    public function down(): void
    {
        Schema::table('guru_tugastambahan', function (Blueprint $table) {
            $table->dropColumn('nama_guru');
        });
    }
};