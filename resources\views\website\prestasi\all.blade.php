@extends('layouts.website')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<style>
.title-container {
    position: relative;
    padding: 20px 0;
    margin-bottom: 50px;
}

.facility-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.facility-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #3498db, #2ecc71);
    border-radius: 2px;
}

.title-decoration {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 2px;
    background-color: #ecf0f1;
}

.title-container::before,
.title-container::after {
    content: '★';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #3498db;
}

.title-container::before {
    left: 25%;
}

.title-container::after {
    right: 25%;
}

@media (max-width: 768px) {
    .facility-title {
        font-size: 2rem;
    }
    .title-container::before {
        left: 10%;
    }
    .title-container::after {
        right: 10%;
    }
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}
.card-img-top {
    height: 250px;
    object-fit: cover;
}

/* Pagination Controls Styling */
.form-select-sm {
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select-sm:focus {
    border-color: #3498db;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Pagination styling */
.pagination {
    margin-top: 2rem;
}

.page-link {
    color: #3498db;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.page-link:hover {
    background-color: #3498db;
    color: #fff;
    border-color: #3498db;
}

.page-item.active .page-link {
    background-color: #3498db;
    border-color: #3498db;
    color: #fff;
}

.page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Responsive pagination controls */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-inline-flex.align-items-center {
        justify-content: center;
    }
}
</style>
<div class="container py-5">
    <div class="title-container text-center">
        <h2 class="facility-title">Semua Prestasi</h2>
        <div class="title-decoration"></div>
    </div>

    <!-- Filter Jenjang -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="list-group list-group-horizontal-md justify-content-center">
                <a href="{{ route('website.prestasi.all') }}" 
                   class="list-group-item list-group-item-action {{ request()->routeIs('website.prestasi.all') ? 'active' : '' }}">
                    Semua
                </a>
                <a href="{{ route('website.prestasi.jenjang', 'paud') }}" 
                   class="list-group-item list-group-item-action {{ request()->segment(2) == 'paud' ? 'active' : '' }}">
                    PAUD
                </a>
                <a href="{{ route('website.prestasi.jenjang', 'sd') }}" 
                   class="list-group-item list-group-item-action {{ request()->segment(2) == 'sd' ? 'active' : '' }}">
                    SD
                </a>
                <a href="{{ route('website.prestasi.jenjang', 'smp') }}" 
                   class="list-group-item list-group-item-action {{ request()->segment(2) == 'smp' ? 'active' : '' }}">
                    SMP
                </a>
                <a href="{{ route('website.prestasi.jenjang', 'sma') }}" 
                   class="list-group-item list-group-item-action {{ request()->segment(2) == 'sma' ? 'active' : '' }}">
                    SMA
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        @forelse($achievements as $achievement)
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                @if($achievement->image)
                    <img src="{{ asset('storage/prestasi/' . $achievement->image) }}" 
                         class="card-img-top" 
                         alt="{{ $achievement->title }}">
                @endif
                <div class="card-body">
                    <h5 class="card-title">{{ $achievement->title }}</h5>
                  <!--  <p class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        {{ $achievement->tanggal ? $achievement->tanggal->format('d M Y') : '-' }}
                    </p>  -->
                    <p class="text-muted">
                        <i class="fas fa-trophy"></i> 
                        {{ $achievement->level }}
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-user"></i> 
                        {{ $achievement->participant }}
                    </p>
                    <p class="card-text">{{ Str::limit($achievement->description, 100) }}</p>
                </div>
            </div>
        </div>
        @empty
        <div class="col-12">
            <div class="alert alert-info text-center">
                Belum ada data prestasi.
            </div>
        </div>
        @endforelse
    </div>

    <!-- Pagination Controls -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="mb-2">
                    <span class="text-muted">
                        @if(isset($perPage) && $perPage === 'all')
                            Menampilkan semua {{ $achievements->total() }} prestasi
                        @else
                            Menampilkan {{ $achievements->firstItem() ?? 0 }} - {{ $achievements->lastItem() ?? 0 }} dari {{ $achievements->total() }} prestasi
                        @endif
                    </span>
                </div>
                <div class="mb-2">
                    <form method="GET" action="{{ route('website.prestasi.all') }}" class="d-inline-flex align-items-center">
                        <label for="per_page" class="me-2 text-muted">Tampilkan:</label>
                        <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                            <option value="9" {{ (isset($perPage) && $perPage == 9) ? 'selected' : '' }}>9</option>
                            <option value="20" {{ (isset($perPage) && $perPage == 20) ? 'selected' : '' }}>20</option>
                            <option value="50" {{ (isset($perPage) && $perPage == 50) ? 'selected' : '' }}>50</option>
                            <option value="100" {{ (isset($perPage) && $perPage == 100) ? 'selected' : '' }}>100</option>
                            <option value="all" {{ (isset($perPage) && $perPage === 'all') ? 'selected' : '' }}>Semua</option>
                        </select>
                        <span class="ms-2 text-muted">per halaman</span>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    @if(isset($perPage) && $perPage !== 'all' && $achievements->hasPages())
    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="Prestasi pagination">
            {{ $achievements->appends(['per_page' => $perPage])->links('pagination::bootstrap-4') }}
        </nav>
    </div>
    @endif
</div>
@endsection
