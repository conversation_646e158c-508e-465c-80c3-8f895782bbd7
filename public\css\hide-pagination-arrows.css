/* Hide Large Pagination Arrow Symbols */

/* Method 1: Hide by content - most effective */
.pagination .page-item .page-link {
    position: relative;
}

/* Hide arrows that are just symbols without text */
.pagination .page-item .page-link:not([rel]):not(.page-number) {
    font-size: 0 !important;
    width: 0 !important;
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    display: none !important;
}

/* Method 2: Target specific arrow symbols */
.pagination .page-link[aria-hidden="true"] {
    display: none !important;
}

/* Method 3: Hide first and last if they don't have rel attribute */
.pagination .page-item:first-child .page-link:not([rel]),
.pagination .page-item:last-child .page-link:not([rel]) {
    display: none !important;
}

/* Method 4: Hide parent items containing only arrows */
.pagination .page-item:first-child:has(.page-link:not([rel])),
.pagination .page-item:last-child:has(.page-link:not([rel])) {
    display: none !important;
}

/* Method 5: JavaScript fallback - hide by text content */
.pagination .page-link[data-arrow-only="true"] {
    display: none !important;
}

/* Ensure Previous/Next text links are visible and styled properly */
.pagination .page-link[rel="prev"],
.pagination .page-link[rel="next"] {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 14px !important;
    padding: 8px 16px !important;
    min-width: 80px !important;
    height: 36px !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Style for numbered page links */
.pagination .page-link:not([rel]):not([aria-hidden]) {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 14px !important;
    padding: 8px 12px !important;
    min-width: 36px !important;
    height: 36px !important;
}

/* Hide any remaining arrow symbols */
.pagination .page-link:empty,
.pagination .page-link:contains("‹"):not(:contains("Previous")),
.pagination .page-link:contains("›"):not(:contains("Next")),
.pagination .page-link:contains("&lsaquo;"):not(:contains("Previous")),
.pagination .page-link:contains("&rsaquo;"):not(:contains("Next")) {
    display: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .pagination .page-link[rel="prev"],
    .pagination .page-link[rel="next"] {
        font-size: 12px !important;
        padding: 6px 12px !important;
        min-width: 70px !important;
        height: 32px !important;
    }
    
    .pagination .page-link:not([rel]):not([aria-hidden]) {
        font-size: 12px !important;
        padding: 6px 10px !important;
        min-width: 32px !important;
        height: 32px !important;
    }
}

@media (max-width: 576px) {
    .pagination .page-link[rel="prev"],
    .pagination .page-link[rel="next"] {
        font-size: 11px !important;
        padding: 5px 10px !important;
        min-width: 60px !important;
        height: 28px !important;
    }
    
    .pagination .page-link:not([rel]):not([aria-hidden]) {
        font-size: 11px !important;
        padding: 5px 8px !important;
        min-width: 28px !important;
        height: 28px !important;
    }
}
