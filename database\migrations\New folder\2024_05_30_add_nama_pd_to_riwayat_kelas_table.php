<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\RiwayatKelas;
use App\Models\PesertaDidik;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('riwayat_kelas', function (Blueprint $table) {
            $table->string('nama_pd')->nullable()->after('siswa_id');
        });

        // Mengisi data nama_pd untuk data yang sudah ada
        $riwayatKelas = RiwayatKelas::all();
        foreach ($riwayatKelas as $riwayat) {
            $siswa = PesertaDidik::find($riwayat->siswa_id);
            if ($siswa) {
                $riwayat->nama_pd = $siswa->nama;
                $riwayat->save();
            }
        }
    }

    public function down(): void
    {
        Schema::table('riwayat_kelas', function (Blueprint $table) {
            $table->dropColumn('nama_pd');
        });
    }
};