// Basic JavaScript for website
console.log('Website loaded successfully');

// Error handling untuk mencegah error yang tidak tertangani
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    return false; // Mencegah error default handling
});

// Error handling untuk Promise yang tidak tertangani
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    e.preventDefault();
});

// Fungsi untuk memastikan jQuery tersedia
function ensureJQuery(callback) {
    if (typeof jQuery !== 'undefined') {
        callback();
    } else {
        console.warn('jQuery not loaded yet, waiting...');
        setTimeout(function() {
            ensureJQuery(callback);
        }, 100);
    }
}

// Inisialisasi setelah DOM ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');

    // Inisialisasi komponen Bootstrap jika ada
    if (typeof bootstrap !== 'undefined') {
        // Inisialisasi tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Inisialisasi popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }

    // Lazy loading untuk iframe YouTube
    const iframes = document.querySelectorAll('iframe[src*="youtube.com"]');
    if (iframes.length > 0) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const iframe = entry.target;
                    if (!iframe.src.includes('autoplay=0')) {
                        iframe.src += '&autoplay=0';
                    }
                    observer.unobserve(iframe);
                }
            });
        });

        iframes.forEach(iframe => observer.observe(iframe));
    }
});

// You can add custom JavaScript here
