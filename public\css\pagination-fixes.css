/* Pagination Arrow Removal - Hide Large Arrow Symbols */

/* Sembunyikan arrow symbols yang besar */
.pagination .page-link[aria-label*="Previous"][aria-hidden="true"],
.pagination .page-link[aria-label*="Next"][aria-hidden="true"],
.pagination .page-link:contains("‹"):not(:contains("Previous")),
.pagination .page-link:contains("›"):not(:contains("Next")) {
    display: none !important;
}

/* Styling untuk pagination links yang tersisa */
.pagination .page-link {
    font-size: 14px !important;
    line-height: 1.2 !important;
    padding: 8px 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: auto !important;
    height: 36px !important;
    text-align: center !important;
}

/* Khusus untuk Previous dan Next text links */
.pagination .page-link[rel="prev"],
.pagination .page-link[rel="next"] {
    min-width: 80px !important;
    font-weight: normal !important;
}

/* Styling untuk pagination container */
.pagination-container {
    display: flex !important;
    justify-content: center !important;
    margin: 2rem 0 !important;
}

.pagination {
    margin: 0 !important;
    gap: 4px !important;
}

/* Responsive pagination */
@media (max-width: 768px) {
    .pagination .page-link {
        font-size: 12px !important;
        padding: 6px 10px !important;
        min-width: 32px !important;
        height: 32px !important;
    }
    
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link {
        font-size: 10px !important;
    }
}

@media (max-width: 576px) {
    .pagination .page-link {
        font-size: 11px !important;
        padding: 5px 8px !important;
        min-width: 28px !important;
        height: 28px !important;
    }
    
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link {
        font-size: 9px !important;
    }
}

/* Sembunyikan arrow symbols yang besar, biarkan text Previous/Next */
.pagination .page-link {
    text-align: center !important;
}

/* Sembunyikan simbol arrow HTML entities */
.pagination .page-link:contains("‹"),
.pagination .page-link:contains("›") {
    display: none !important;
}

/* Jika ada arrow symbols, sembunyikan */
.pagination .page-item:first-child .page-link[aria-hidden="true"],
.pagination .page-item:last-child .page-link[aria-hidden="true"] {
    display: none !important;
}

/* Pastikan text Previous/Next tetap terlihat */
.pagination .page-link[rel="prev"],
.pagination .page-link[rel="next"] {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Active state */
.pagination .page-item.active .page-link {
    font-size: 14px !important;
}

/* Disabled state */
.pagination .page-item.disabled .page-link {
    opacity: 0.5 !important;
}

/* Sembunyikan pagination items yang hanya berisi arrow symbols */
.pagination .page-item .page-link[aria-hidden="true"] {
    display: none !important;
}

/* Sembunyikan berdasarkan content */
.pagination .page-link:empty,
.pagination .page-link:contains("‹"):not(:contains("Previous")),
.pagination .page-link:contains("›"):not(:contains("Next")) {
    display: none !important;
}

/* Alternatif: sembunyikan parent item jika hanya berisi arrow */
.pagination .page-item:has(.page-link[aria-hidden="true"]) {
    display: none !important;
}

/* Force hide specific arrow-only links */
.pagination .page-item:first-child .page-link:not([rel]),
.pagination .page-item:last-child .page-link:not([rel]) {
    display: none !important;
}

/* Show only links with rel="prev" or rel="next" */
.pagination .page-link[rel="prev"],
.pagination .page-link[rel="next"] {
    display: flex !important;
}
