@extends('layouts.website')

@section('content')
<style>
    .visimisi-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    .visimisi-title {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
        font-size: 2.5rem;
        text-align: center;
        margin-bottom: 2.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: relative;
    }
    .visimisi-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }
    .visimisi-content {
        background: #fff;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        padding: 2rem 2rem 2.5rem 2rem;
        font-size: 1.08rem;
        color: #444;
        line-height: 1.7;
        margin-bottom: 2rem;
    }
    @media (max-width: 768px) {
        .visimisi-title {
            font-size: 2rem;
        }
        .visimisi-content {
            padding: 1rem;
        }
    }
    @media (max-width: 576px) {
        .visimisi-title {
            font-size: 1.5rem;
        }
    }
</style>
<div class="visimisi-container">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                @include('website.partials._sidebar')
            </div>
            <!-- Content -->
            <div class="col-md-9">
                <h1 class="visimisi-title">VISI & MISI {{ strtoupper($unit->nama_unit) }}</h1>
                <div class="visimisi-content">
                    {!! $visiMisi->konten !!}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
