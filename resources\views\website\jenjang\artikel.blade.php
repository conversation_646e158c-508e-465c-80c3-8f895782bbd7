@extends('layouts.website')
<!-- Artikel jenjang -->
@section('title', 'Artikel ' . $unit->nama_unit)

@section('content')
<style>
/* ===== STYLING UNTUK HALAMAN ARTIKEL ===== */

/* Container utama dengan background gradient */
.artikel-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Styling untuk judul halaman */
.page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Styling untuk card artikel */
.artikel-card {
    background: white;
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.artikel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.artikel-card:hover::before {
    transform: scaleX(1);
}

.artikel-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Styling untuk gambar card */
.artikel-image {
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
    border-radius: 0;
}

.artikel-card:hover .artikel-image {
    transform: scale(1.05);
}

/* Container gambar */
.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
}

/* Styling untuk body card */
.artikel-card-body {
    padding: 1.5rem;
    background: white;
}

.artikel-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
    line-height: 1.4;
}

.artikel-card:hover .artikel-title {
    color: #667eea;
}

.artikel-excerpt {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Styling untuk tombol baca selengkapnya */
.btn-read-more {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-read-more::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-read-more:hover::before {
    left: 100%;
}

.btn-read-more:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

/* Styling untuk placeholder jika tidak ada gambar */
.no-image-placeholder {
    height: 220px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 3rem;
    border-radius: 20px 20px 0 0;
}

/* Styling untuk empty state */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

/* Pagination Controls Styling */
.form-select-sm {
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select-sm:focus {
    border-color: #667eea;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Styling untuk pagination */
.pagination-container {
    margin-top: 3rem;
}

.pagination .page-link {
    border: 1px solid #dee2e6;
    color: #667eea;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Responsive design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .artikel-container {
        padding: 1rem 0;
    }

    .artikel-image,
    .no-image-placeholder {
        height: 180px;
    }

    .artikel-card-body {
        padding: 1rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-inline-flex.align-items-center {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 1.75rem;
    }

    .artikel-image,
    .no-image-placeholder {
        height: 160px;
    }
}
</style>

<div class="artikel-container">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                @include('website.partials._sidebar')
            </div>

            <!-- Content -->
            <div class="col-md-9">
                <h1 class="page-title">ARTIKEL {{ strtoupper($unit->nama_unit) }}</h1>

                @if($articles->count() > 0)
                    <div class="row">
                        @foreach ($articles as $article)
                            <div class="col-lg-6 col-md-12 mb-4">
                                <div class="card artikel-card">
                                    <div class="image-container">
                                        @if($article->image)
                                            <img src="{{ asset('storage/' . $article->image) }}"
                                                 class="card-img-top artikel-image"
                                                 alt="{{ $article->title }}">
                                        @else
                                            <div class="no-image-placeholder">
                                                <i class="fas fa-newspaper"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="card-body artikel-card-body">
                                        <h5 class="card-title artikel-title">{{ $article->title }}</h5>
                                        <p class="card-text artikel-excerpt">{{ $article->excerpt }}</p>
                                        <a href="{{ route('website.artikel.show', $article->slug) }}"
                                           class="btn btn-read-more">
                                            <i class="fas fa-book-open me-2"></i>Baca Selengkapnya
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state">
                        <i class="fas fa-newspaper"></i>
                        <h3>Belum Ada Artikel</h3>
                        <p>Artikel untuk {{ $unit->nama_unit }} belum tersedia.</p>
                    </div>
                @endif

                <!-- Pagination Controls -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <div class="mb-2">
                                <span class="text-muted">
                                    @if(isset($perPage) && $perPage === 'all')
                                        Menampilkan semua {{ $articles->total() }} artikel
                                    @else
                                        Menampilkan {{ $articles->firstItem() ?? 0 }} - {{ $articles->lastItem() ?? 0 }} dari {{ $articles->total() }} artikel
                                    @endif
                                </span>
                            </div>
                            <div class="mb-2">
                                <form method="GET" action="{{ route('website.jenjang.artikel', $jenjang) }}" class="d-inline-flex align-items-center">
                                    <label for="per_page" class="me-2 text-muted">Tampilkan:</label>
                                    <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                                        <option value="9" {{ (isset($perPage) && $perPage == 9) ? 'selected' : '' }}>9</option>
                                        <option value="20" {{ (isset($perPage) && $perPage == 20) ? 'selected' : '' }}>20</option>
                                        <option value="50" {{ (isset($perPage) && $perPage == 50) ? 'selected' : '' }}>50</option>
                                        <option value="100" {{ (isset($perPage) && $perPage == 100) ? 'selected' : '' }}>100</option>
                                        <option value="all" {{ (isset($perPage) && $perPage === 'all') ? 'selected' : '' }}>Semua</option>
                                    </select>
                                    <span class="ms-2 text-muted">per halaman</span>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if(isset($perPage) && $perPage !== 'all' && $articles->hasPages())
                <div class="pagination-container d-flex justify-content-center mt-4">
                    <nav aria-label="Artikel pagination">
                        {{ $articles->appends(['per_page' => $perPage])->links('pagination::bootstrap-4') }}
                    </nav>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection