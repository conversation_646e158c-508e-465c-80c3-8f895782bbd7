<?php $__env->startSection('title', '<PERSON><PERSON><PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="alert alert-danger">
        <h4><i class="icon fas fa-ban"></i> <PERSON><PERSON><PERSON></h4>
        <p>Anda tidak memiliki hak akses untuk halaman ini.</p>
        <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-primary">
            <i class="fas fa-home"></i> Kembali ke Dashboard
        </a>
    </div>

    <?php if(config('app.debug')): ?>
    <div class="card card-info">
        <div class="card-header">
            <h3 class="card-title">Debug Information</h3>
        </div>
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-3">User ID</dt>
                <dd class="col-sm-9"><?php echo e(auth()->id()); ?></dd>

                <dt class="col-sm-3">Name</dt>
                <dd class="col-sm-9"><?php echo e(auth()->user()->name ?? 'Not authenticated'); ?></dd>

                <dt class="col-sm-3">Role</dt>
                <dd class="col-sm-9"><?php echo e(auth()->user()->role ?? 'No role'); ?></dd>

                <dt class="col-sm-3">URL</dt>
                <dd class="col-sm-9"><?php echo e(request()->url()); ?></dd>

                <dt class="col-sm-3">Method</dt>
                <dd class="col-sm-9"><?php echo e(request()->method()); ?></dd>
            </dl>

            <?php if(isset($debug_info)): ?>
            <h6>Exception Info:</h6>
            <pre><?php echo e(print_r($debug_info, true)); ?></pre>
            <?php endif; ?>

            <?php if(auth()->check()): ?>
            <h6>Permissions:</h6>
            <ul class="list-group">
                <?php $__currentLoopData = auth()->user()->getAllPermissions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="list-group-item"><?php echo e($permission->name); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<style>
    .alert {
        margin-top: 20px;
    }
    pre {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/errors/403.blade.php ENDPATH**/ ?>