@extends('adminlte::page')

@section('css')
<style>
/* Pagination Controls Styling */
.form-select-sm {
    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select-sm:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive pagination controls */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-inline-flex.align-items-center {
        justify-content: center;
    }
}
</style>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Event</h3>
        <div class="card-tools">
            <a href="{{ route('admin.website.event.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Event
            </a>
        </div>
    </div>
    <div class="card-body">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Judul</th>
                    <th>Tanggal</th>
                    <th>Lokasi</th>
                    @role('Administrator')
                    <th>Unit</th>
                    @endrole
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @foreach($events as $event)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $event->judul }}</td>
                    <td>{{ \Carbon\Carbon::parse($event->tanggal)->format('d/m/Y') }}</td>
                    <td>{{ $event->lokasi }}</td>
                    @role('Administrator')
                    <td>{{ $event->unit->nama_unit ?? '-' }}</td>
                    @endrole
                    <td>
                        <a href="{{ route('admin.website.event.edit', $event->id) }}" 
                           class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.website.event.destroy', $event->id) }}" 
                              method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" 
                                    onclick="return confirm('Yakin ingin menghapus?')">
                                Hapus
                            </button>
                        </form>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Pagination Controls -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="mb-2">
                        <span class="text-muted">
                            @if(isset($perPage) && $perPage === 'all')
                                Menampilkan semua {{ $events->total() }} event
                            @else
                                Menampilkan {{ $events->firstItem() ?? 0 }} - {{ $events->lastItem() ?? 0 }} dari {{ $events->total() }} event
                            @endif
                        </span>
                    </div>
                    <div class="mb-2">
                        <form method="GET" action="{{ route('admin.website.event.index') }}" class="d-inline-flex align-items-center">
                            <label for="per_page" class="me-2 text-muted">Tampilkan:</label>
                            <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                                <option value="10" {{ (isset($perPage) && $perPage == 10) ? 'selected' : '' }}>10</option>
                                <option value="20" {{ (isset($perPage) && $perPage == 20) ? 'selected' : '' }}>20</option>
                                <option value="50" {{ (isset($perPage) && $perPage == 50) ? 'selected' : '' }}>50</option>
                                <option value="100" {{ (isset($perPage) && $perPage == 100) ? 'selected' : '' }}>100</option>
                                <option value="all" {{ (isset($perPage) && $perPage === 'all') ? 'selected' : '' }}>Semua</option>
                            </select>
                            <span class="ms-2 text-muted">per halaman</span>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        @if(isset($perPage) && $perPage !== 'all' && $events->hasPages())
        <div class="d-flex justify-content-center mt-3">
            <nav aria-label="Event pagination">
               <!-- {{ $events->appends(['per_page' => $perPage])->links() }} -->
            </nav>
        </div>
        @endif
    </div>
</div>
@endsection


