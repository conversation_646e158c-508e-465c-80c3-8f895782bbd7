<?php $__env->startSection('title', 'Manajemen Artikel'); ?>

<?php $__env->startSection('content_header'); ?>
    <h1>Manajemen Artikel</h1>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Artikel</h3>
        <div class="card-tools">
            <a href="<?php echo e(route('admin.website.artikel.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Artikel
            </a>
        </div>
    </div>
    <div class="card-body">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        <?php endif; ?>

        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th style="width: 50px">No</th>
                    <th style="width: 150px">Gambar</th>
                    <th>Judul</th>
                    <th>Status</th>
                    <th>Penulis</th>
                    <th>Tanggal Publikasi</th>
                    <th style="width: 180px">Aksi</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($loop->iteration); ?></td>
                    <td>
                        <img src="<?php echo e(asset('storage/' . $article->image)); ?>" 
                             alt="<?php echo e($article->title); ?>" 
                             class="img-thumbnail"
                             style="max-height: 100px">
                    </td>
                    <td><?php echo e($article->title); ?></td>
                    <td>
                        <span class="badge badge-<?php echo e($article->status === 'published' ? 'success' : ($article->status === 'draft' ? 'warning' : 'secondary')); ?>">
                            <?php echo e(ucfirst($article->status)); ?>

                        </span>
                    </td>
                    <td><?php echo e($article->writer ?? $article->author->name); ?></td>
                    <td><?php echo e($article->published_at ? $article->published_at->format('d/m/Y H:i') : '-'); ?></td>
                    <td>
                        <?php if($article->status === 'published'): ?>
                            <a href="<?php echo e(route('website.artikel.show', $article->slug)); ?>"
                               class="btn btn-sm btn-success"
                               target="_blank"
                               title="Lihat Artikel">
                                <i class="fas fa-eye"></i>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('admin.website.artikel.edit', $article)); ?>"
                           class="btn btn-sm btn-info"
                           title="Edit Artikel">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="<?php echo e(route('admin.website.artikel.destroy', $article)); ?>"
                              method="POST"
                              class="d-inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit"
                                    class="btn btn-sm btn-danger"
                                    onclick="return confirm('Apakah Anda yakin ingin menghapus artikel ini?')"
                                    title="Hapus Artikel">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="7" class="text-center">Tidak ada artikel</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- Pagination Controls -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="mb-2">
                        <span class="text-muted">
                            <?php if(isset($perPage) && $perPage === 'all'): ?>
                                Menampilkan semua <?php echo e($articles->total()); ?> artikel
                            <?php else: ?>
                                Menampilkan <?php echo e($articles->firstItem() ?? 0); ?> - <?php echo e($articles->lastItem() ?? 0); ?> dari <?php echo e($articles->total()); ?> artikel
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="mb-2">
                        <form method="GET" action="<?php echo e(route('admin.website.artikel.index')); ?>" class="d-inline-flex align-items-center">
                            <label for="per_page" class="me-2 text-muted">Tampilkan:</label>
                            <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                                <option value="10" <?php echo e((isset($perPage) && $perPage == 10) ? 'selected' : ''); ?>>10</option>
                                <option value="20" <?php echo e((isset($perPage) && $perPage == 20) ? 'selected' : ''); ?>>20</option>
                                <option value="50" <?php echo e((isset($perPage) && $perPage == 50) ? 'selected' : ''); ?>>50</option>
                                <option value="100" <?php echo e((isset($perPage) && $perPage == 100) ? 'selected' : ''); ?>>100</option>
                                <option value="all" <?php echo e((isset($perPage) && $perPage === 'all') ? 'selected' : ''); ?>>Semua</option>
                            </select>
                            <span class="ms-2 text-muted">per halaman</span>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <?php if(isset($perPage) && $perPage !== 'all' && $articles->hasPages()): ?>
       <!--  <div class="d-flex justify-content-center mt-3">
            <nav aria-label="Artikel pagination">
                <?php echo e($articles->appends(['per_page' => $perPage])->links()); ?>

            </nav>
        </div> -->
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" href="/css/admin_custom.css">
    <style>
    /* Pagination Controls Styling */
    .form-select-sm {
        padding: 0.25rem 1.5rem 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
        border: 1px solid #dee2e6;
        background-color: #fff;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-select-sm:focus {
        border-color: #007bff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Responsive pagination controls */
    @media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }

        .d-inline-flex.align-items-center {
            justify-content: center;
        }
    }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script>
        $(document).ready(function() {
            // Auto close alert after 3 seconds
            setTimeout(function() {
                $(".alert").alert('close');
            }, 3000);
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/website/artikel/index.blade.php ENDPATH**/ ?>