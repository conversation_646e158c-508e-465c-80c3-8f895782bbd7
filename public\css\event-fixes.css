/* Event Page Error Fixes CSS */

/* Bootstrap 5 Ratio Fix untuk YouTube Embeds */
.ratio {
    position: relative;
    width: 100%;
}

.ratio::before {
    display: block;
    padding-top: var(--bs-aspect-ratio);
    content: "";
}

.ratio > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.ratio-16x9 {
    --bs-aspect-ratio: calc(9 / 16 * 100%);
}

/* Override untuk media container */
.media-container .ratio {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
}

.media-container .ratio::before {
    display: none !important;
    padding-top: 0 !important;
}

/* YouTube iframe fixes */
.ratio iframe[src*="youtube.com"],
.ratio iframe[src*="youtu.be"] {
    border: none !important;
    border-radius: inherit;
}

/* Error prevention untuk missing images */
img[src=""], img:not([src]) {
    display: none;
}

/* Fallback untuk broken YouTube embeds */
iframe[src*="youtube.com"]:not([src*="embed"]) {
    display: none;
}

/* Console error prevention */
.embed-responsive-item {
    border: none !important;
}

/* Prevent layout shift */
.media-container {
    min-height: 200px;
    background-color: #f8f9fa;
}

/* Loading state untuk iframe */
iframe {
    background-color: #f8f9fa;
    transition: opacity 0.3s ease;
}

iframe:not([src]) {
    opacity: 0;
}

/* Error handling untuk Font Awesome icons */
.fas, .fa {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Pro", FontAwesome, sans-serif !important;
    font-weight: 900;
}

/* Prevent JavaScript errors dari CSS animations */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Fix untuk Bootstrap tooltip/popover errors */
.tooltip, .popover {
    z-index: 1070;
}

/* Prevent console errors dari missing Bootstrap classes */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }

/* Safe fallbacks */
.btn:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-warning):not(.btn-info):not(.btn-light):not(.btn-dark) {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

/* Prevent layout errors */
.container-fluid, .container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* Error prevention untuk card components */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 0.25rem;
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

/* Fix untuk responsive utilities */
@media (max-width: 575.98px) {
    .d-sm-none { display: none !important; }
}

@media (max-width: 767.98px) {
    .d-md-none { display: none !important; }
}

@media (max-width: 991.98px) {
    .d-lg-none { display: none !important; }
}

@media (max-width: 1199.98px) {
    .d-xl-none { display: none !important; }
}

/* Pagination Arrow Fixes */
.pagination .page-link {
    font-size: 14px !important;
    line-height: 1.4 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 40px !important;
    height: 40px !important;
}

/* Fix untuk tanda panah yang terlalu besar */
.pagination .page-link:contains("‹"),
.pagination .page-link:contains("›") {
    font-size: 16px !important;
}

/* Custom arrow styling */
.pagination .page-item:first-child .page-link::before {
    content: "‹" !important;
    font-size: 16px !important;
    font-weight: bold !important;
}

.pagination .page-item:last-child .page-link::before {
    content: "›" !important;
    font-size: 16px !important;
    font-weight: bold !important;
}

/* Hide original arrow text */
.pagination .page-item:first-child .page-link,
.pagination .page-item:last-child .page-link {
    text-indent: -9999px !important;
    position: relative !important;
}

.pagination .page-item:first-child .page-link::before,
.pagination .page-item:last-child .page-link::before {
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    text-indent: 0 !important;
}

/* Alternative: Replace with smaller arrows */
.pagination .page-link[aria-label*="Previous"]::after {
    content: "←" !important;
    font-size: 14px !important;
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
}

.pagination .page-link[aria-label*="Next"]::after {
    content: "→" !important;
    font-size: 14px !important;
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
}

/* Hide original text for arrow links */
.pagination .page-link[aria-label*="Previous"],
.pagination .page-link[aria-label*="Next"] {
    color: transparent !important;
    position: relative !important;
}
