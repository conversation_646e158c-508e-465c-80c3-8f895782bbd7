@extends('adminlte::page')

@section('title', 'Edit Event')

@section('content_header')
    <h1>Edit Event</h1>
@stop

@section('content')
<div class="card">
    <div class="card-body">
        <form action="{{ route('admin.website.event.update', $event->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <div class="form-group">
                <label for="judul">Judul Event <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control @error('judul') is-invalid @enderror" 
                       id="judul" 
                       name="judul" 
                       value="{{ old('judul', $event->judul) }}" 
                       required>
                @error('judul')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="deskripsi">Deskripsi <span class="text-danger">*</span></label>
                <textarea class="form-control @error('deskripsi') is-invalid @enderror" 
                          id="deskripsi" 
                          name="deskripsi" 
                          rows="5" 
                          required>{{ old('deskripsi', $event->deskripsi) }}</textarea>
                @error('deskripsi')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="tanggal">Tanggal Event <span class="text-danger">*</span></label>
                <input type="date" 
                       class="form-control @error('tanggal') is-invalid @enderror" 
                       id="tanggal" 
                       name="tanggal" 
                       value="{{ old('tanggal', $event->tanggal) }}" 
                       required>
                @error('tanggal')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="lokasi">Lokasi <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control @error('lokasi') is-invalid @enderror" 
                       id="lokasi" 
                       name="lokasi" 
                       value="{{ old('lokasi', $event->lokasi) }}" 
                       required>
                @error('lokasi')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label>Jenis Media</label>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" id="jenis_media_gambar" name="jenis_media" class="custom-control-input" value="gambar" {{ $event->youtube_url ? '' : 'checked' }}>
                    <label class="custom-control-label" for="jenis_media_gambar">Gambar</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" id="jenis_media_youtube" name="jenis_media" class="custom-control-input" value="youtube" {{ $event->youtube_url ? 'checked' : '' }}>
                    <label class="custom-control-label" for="jenis_media_youtube">Video YouTube</label>
                </div>
            </div>

            <div class="form-group" id="gambar-container" {{ $event->youtube_url ? 'style="display: none;"' : '' }}>
                <label for="gambar">Gambar Event</label>
                @if($event->gambar)
                    <div class="mb-2">
                        <img src="{{ asset('storage/events/'.$event->gambar) }}" alt="{{ $event->judul }}" class="img-thumbnail" width="200">
                    </div>
                @endif
                <div class="custom-file">
                    <input type="file" 
                           class="custom-file-input @error('gambar') is-invalid @enderror" 
                           id="gambar" 
                           name="gambar"
                           accept="image/jpeg,image/png,image/jpg">
                    <label class="custom-file-label" for="gambar">{{ $event->gambar ? $event->gambar : 'Pilih file' }}</label>
                    <small class="form-text text-muted">Format: JPEG, PNG, JPG. Maksimal 2MB. Biarkan kosong jika tidak ingin mengubah gambar.</small>
                </div>
                @error('gambar')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group" id="youtube-container" {{ $event->youtube_url ? '' : 'style="display: none;"' }}>
                <label for="youtube_url">Link YouTube</label>
                <input type="text" class="form-control @error('youtube_url') is-invalid @enderror" 
                       id="youtube_url" name="youtube_url" value="{{ old('youtube_url', $event->youtube_url) }}" 
                       placeholder="Contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX">
                <small class="form-text text-muted">Masukkan URL video YouTube.</small>
                @error('youtube_url')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                @if($event->youtube_url && getYoutubeVideoId($event->youtube_url))
                    <div class="mt-2">
                        <div class="ratio ratio-16x9">
                            <iframe src="https://www.youtube.com/embed/{{ getYoutubeVideoId($event->youtube_url) }}?rel=0&modestbranding=1"
                                    title="Preview {{ $event->judul }}"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen></iframe>
                        </div>
                    </div>
                @endif
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Update</button>
                <a href="{{ route('admin.website.event.index') }}" class="btn btn-secondary">Batal</a>
            </div>
        </form>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        // Menampilkan nama file yang dipilih pada input file
        $('input[type="file"]').change(function(e){
            var fileName = e.target.files[0].name;
            $('.custom-file-label').html(fileName);
        });

        // Menangani perubahan pada radio button jenis media
        $('input[name="jenis_media"]').change(function() {
            if ($(this).val() === 'gambar') {
                $('#gambar-container').show();
                $('#youtube-container').hide();
            } else {
                $('#gambar-container').hide();
                $('#youtube-container').show();
            }
        });
    </script>
@stop




