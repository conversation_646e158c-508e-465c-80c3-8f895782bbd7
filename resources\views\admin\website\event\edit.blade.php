@extends('layouts.admin')

@section('content')
<div class="card">
    <div class="card-body">
        <form action="{{ route('admin.website.event.update', $event->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <div class="form-group">
                <label for="judul">Judul Event</label>
                <input type="text" class="form-control @error('judul') is-invalid @enderror" 
                       id="judul" name="judul" value="{{ old('judul', $event->judul) }}" required>
                @error('judul')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            @role('Administrator')
            <div class="form-group">
                <label for="unit_id">Unit</label>
                <select class="form-control @error('unit_id') is-invalid @enderror" 
                        id="unit_id" name="unit_id" required>
                    <option value="">Pilih Unit</option>
                    @foreach($units as $unit)
                        <option value="{{ $unit->id }}" 
                            {{ old('unit_id', $event->unit_id) == $unit->id ? 'selected' : '' }}>
                            {{ $unit->nama_unit }}
                        </option>
                    @endforeach
                </select>
                @error('unit_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            @else
                <input type="hidden" name="unit_id" value="{{ auth()->user()->unit_id }}">
            @endrole

            <!-- Field lainnya -->
            <div class="form-group">
                <label for="tanggal">Tanggal Event</label>
                <input type="date" class="form-control @error('tanggal') is-invalid @enderror" 
                       id="tanggal" name="tanggal" value="{{ old('tanggal', $event->tanggal) }}" required>
            </div>

            <div class="form-group">
                <label for="lokasi">Lokasi</label>
                <input type="text" class="form-control @error('lokasi') is-invalid @enderror" 
                       id="lokasi" name="lokasi" value="{{ old('lokasi', $event->lokasi) }}" required>
            </div>

            <div class="form-group">
                <label for="deskripsi">Deskripsi</label>
                <textarea class="form-control @error('deskripsi') is-invalid @enderror" 
                          id="deskripsi" name="deskripsi" rows="5" required>{{ old('deskripsi', $event->deskripsi) }}</textarea>
                @error('deskripsi')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label>Jenis Media</label>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" id="jenis_media_gambar" name="jenis_media" class="custom-control-input" value="gambar" {{ $event->youtube_url ? '' : 'checked' }}>
                    <label class="custom-control-label" for="jenis_media_gambar">Gambar</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" id="jenis_media_youtube" name="jenis_media" class="custom-control-input" value="youtube" {{ $event->youtube_url ? 'checked' : '' }}>
                    <label class="custom-control-label" for="jenis_media_youtube">Video YouTube</label>
                </div>
            </div>

            <div class="form-group" id="gambar-container">
                <label for="gambar">Gambar Event</label>
                @if($event->gambar)
                    <div class="mb-2">
                        <img src="{{ asset('storage/events/'.$event->gambar) }}" alt="{{ $event->judul }}" class="img-thumbnail" width="200">
                    </div>
                @endif
                <input type="file" class="form-control-file @error('gambar') is-invalid @enderror" 
                       id="gambar" name="gambar" accept="image/jpeg,image/png,image/jpg">
                <small class="form-text text-muted">Format: JPEG, PNG, JPG. Maksimal 2MB. Biarkan kosong jika tidak ingin mengubah gambar.</small>
                @error('gambar')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group" id="youtube-container" style="display: none;">
                <label for="youtube_url">Link YouTube</label>
                <input type="text" class="form-control @error('youtube_url') is-invalid @enderror" 
                       id="youtube_url" name="youtube_url" value="{{ old('youtube_url', $event->youtube_url) }}" 
                       placeholder="Contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX">
                <small class="form-text text-muted">Masukkan URL video YouTube.</small>
                @error('youtube_url')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                @if($event->youtube_url)
                    <div class="mt-2">
                        <p><strong>Video URL saat ini:</strong> {{ $event->youtube_url }}</p>
                    </div>
                @endif
            </div>

            <button type="submit" class="btn btn-primary">Update</button>
        </form>
    </div>
</div>
<a href="{{ route('admin.website.event.index') }}" class="btn btn-secondary">Batal</a>
@endsection

@section('js')
<script>
    // Fungsi untuk menampilkan form sesuai jenis media yang dipilih
    $(document).ready(function() {
        // Set kondisi awal berdasarkan data yang ada
        if ($('#jenis_media_youtube').is(':checked')) {
            $('#gambar-container').hide();
            $('#youtube-container').show();
        } else {
            $('#gambar-container').show();
            $('#youtube-container').hide();
        }
        
        // Menangani perubahan pada radio button jenis media
        $('input[name="jenis_media"]').change(function() {
            if ($(this).val() === 'gambar') {
                $('#gambar-container').show();
                $('#youtube-container').hide();
            } else {
                $('#gambar-container').hide();
                $('#youtube-container').show();
            }
        });
    });
</script>
@endsection




