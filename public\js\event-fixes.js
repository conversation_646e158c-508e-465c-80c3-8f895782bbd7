// Event Page JavaScript Fixes
(function() {
    'use strict';

    // Error handling untuk YouTube iframe
    function handleYouTubeErrors() {
        const iframes = document.querySelectorAll('iframe[src*="youtube.com"]');
        
        iframes.forEach(function(iframe) {
            // Add error handling
            iframe.addEventListener('error', function() {
                console.warn('YouTube iframe failed to load:', iframe.src);
                // Replace with placeholder
                const placeholder = document.createElement('div');
                placeholder.className = 'youtube-error-placeholder';
                placeholder.innerHTML = '<i class="fas fa-exclamation-triangle"></i><br>Video tidak dapat dimuat';
                placeholder.style.cssText = `
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                    height: 100%;
                    background-color: #f8f9fa;
                    color: #6c757d;
                    border-radius: inherit;
                `;
                iframe.parentNode.replaceChild(placeholder, iframe);
            });

            // Add loading state
            iframe.addEventListener('load', function() {
                iframe.style.opacity = '1';
            });
        });
    }

    // Fix untuk Bootstrap 5 ratio
    function fixBootstrapRatio() {
        const ratios = document.querySelectorAll('.ratio');
        
        ratios.forEach(function(ratio) {
            if (!ratio.style.getPropertyValue('--bs-aspect-ratio')) {
                if (ratio.classList.contains('ratio-16x9')) {
                    ratio.style.setProperty('--bs-aspect-ratio', 'calc(9 / 16 * 100%)');
                }
            }
        });
    }

    // Prevent console errors dari missing elements
    function preventConsoleErrors() {
        // Override console.error untuk filter known issues
        const originalError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            
            // Filter out known Bootstrap/jQuery issues
            if (message.includes('Bootstrap') || 
                message.includes('Popper') ||
                message.includes('tooltip') ||
                message.includes('popover')) {
                return;
            }
            
            originalError.apply(console, args);
        };
    }

    // Fix untuk missing Font Awesome icons
    function fixFontAwesome() {
        const icons = document.querySelectorAll('.fas, .fa, .far, .fab');
        
        icons.forEach(function(icon) {
            if (getComputedStyle(icon).fontFamily.indexOf('Font Awesome') === -1) {
                // Fallback untuk missing Font Awesome
                icon.style.fontFamily = '"Font Awesome 5 Free", "Font Awesome 5 Pro", FontAwesome, sans-serif';
                icon.style.fontWeight = '900';
            }
        });
    }

    // Lazy loading untuk iframe
    function setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const iframes = document.querySelectorAll('iframe[data-src]');
            
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const iframe = entry.target;
                        iframe.src = iframe.dataset.src;
                        iframe.removeAttribute('data-src');
                        observer.unobserve(iframe);
                    }
                });
            });
            
            iframes.forEach(function(iframe) {
                observer.observe(iframe);
            });
        }
    }

    // Fix untuk card hover effects
    function fixCardHoverEffects() {
        const cards = document.querySelectorAll('.event-card, .card');

        cards.forEach(function(card) {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }

    // Fix untuk pagination - hapus arrow symbols yang besar
    function fixPaginationArrows() {
        const paginationLinks = document.querySelectorAll('.pagination .page-link');

        paginationLinks.forEach(function(link) {
            const text = link.textContent.trim();

            // Tandai links yang hanya berisi arrow symbols
            if (text === '‹' || text === '›' || text === '&lsaquo;' || text === '&rsaquo;') {
                link.setAttribute('data-arrow-only', 'true');
                link.style.display = 'none';
                // Sembunyikan parent item juga
                if (link.parentElement) {
                    link.parentElement.style.display = 'none';
                }
            }

            // Pastikan Previous/Next text links tetap terlihat
            if (text.includes('Previous') || text.includes('Next')) {
                link.style.display = 'flex';
                link.style.alignItems = 'center';
                link.style.justifyContent = 'center';
            }
        });

        // Hapus pagination items yang hanya berisi arrow symbols
        const paginationItems = document.querySelectorAll('.pagination .page-item');
        paginationItems.forEach(function(item) {
            const link = item.querySelector('.page-link');
            if (link) {
                const text = link.textContent.trim();
                if (text === '‹' || text === '›' || text === '&lsaquo;' || text === '&rsaquo;') {
                    item.style.display = 'none';
                }
            }
        });

        console.log('Pagination arrows hidden successfully');
    }

    // Initialize semua fixes
    function initEventFixes() {
        try {
            handleYouTubeErrors();
            fixBootstrapRatio();
            preventConsoleErrors();
            fixFontAwesome();
            setupLazyLoading();
            fixCardHoverEffects();
            fixPaginationArrows();

            console.log('Event page fixes initialized successfully');
        } catch (error) {
            console.warn('Some event fixes failed to initialize:', error);
        }
    }

    // Run fixes when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initEventFixes);
    } else {
        initEventFixes();
    }

    // Re-run fixes after AJAX content loads
    window.addEventListener('load', function() {
        setTimeout(initEventFixes, 1000);
    });

    // Export untuk debugging
    window.EventFixes = {
        init: initEventFixes,
        handleYouTubeErrors: handleYouTubeErrors,
        fixBootstrapRatio: fixBootstrapRatio,
        fixPaginationArrows: fixPaginationArrows
    };

})();
