<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kelas', function (Blueprint $table) {
            // Cek apakah kolom sudah ada sebelum menambahkannya
            if (!Schema::hasColumn('kelas', 'unit_id')) {
                $table->bigInteger('unit_id')->unsigned()->after('id')->nullable();
                $table->foreign('unit_id')->references('id')->on('units');
            }
            
            if (!Schema::hasColumn('kelas', 'gedung_id')) {
                $table->bigInteger('gedung_id')->unsigned()->after('jenjang_id')->nullable();
                $table->foreign('gedung_id')->references('id')->on('gedungs');
            }
            
            if (!Schema::hasColumn('kelas', 'tingkat')) {
                $table->string('tingkat')->nullable()->after('nama');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kelas', function (Blueprint $table) {
            // Hapus foreign key terlebih dahulu
            if (Schema::hasColumn('kelas', 'unit_id')) {
                $table->dropForeign(['unit_id']);
                $table->dropColumn('unit_id');
            }
            
            if (Schema::hasColumn('kelas', 'gedung_id')) {
                $table->dropForeign(['gedung_id']);
                $table->dropColumn('gedung_id');
            }
            
            if (Schema::hasColumn('kelas', 'tingkat')) {
                $table->dropColumn('tingkat');
            }
        });
    }
};