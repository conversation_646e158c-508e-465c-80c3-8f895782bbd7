<!-- Prestasi Per Jenjang -->
<?php $__env->startSection('title', 'Prestasi' . strtoupper($jenjang)); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <div class="row">
        <!-- Sidebar Menu -->
        <div class="col-md-3">
            <?php echo $__env->make('website.partials._sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="title-container text-center">
                <h2 class="facility-title">Prestasi <?php echo e(strtoupper($jenjang)); ?></h2>
                <div class="title-decoration"></div>
            </div>
            <style>
                .title-container {
                    position: relative;
                    padding: 20px 0;
                    margin-bottom: 50px;
                }
                .facility-title {
                    font-size: 2.5rem;
                    font-weight: 800;
                    color: #2c3e50;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                    margin-bottom: 20px;
                    position: relative;
                    display: inline-block;
                    padding-bottom: 15px;
                }
                .facility-title::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 80px;
                    height: 4px;
                    background: linear-gradient(to right, #3498db, #2ecc71);
                    border-radius: 2px;
                }
                .title-decoration {
                    position: absolute;
                    bottom: -10px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 150px;
                    height: 2px;
                    background-color: #ecf0f1;
                }
                .title-container::before,
                .title-container::after {
                    content: '★';
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    font-size: 24px;
                    color: #3498db;
                }
                .title-container::before {
                    left: 25%;
                }
                .title-container::after {
                    right: 25%;
                }
                @media (max-width: 768px) {
                    .facility-title {
                        font-size: 2rem;
                    }
                    .title-container::before {
                        left: 10%;
                    }
                    .title-container::after {
                        right: 10%;
                    }
                }
                .card {
                    transition: transform 0.3s ease, box-shadow 0.3s ease;
                }
                .card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
                }
                .card-img-top {
                    height: 250px;
                    object-fit: cover;
                }

                /* Pagination Controls Styling */
                .form-select-sm {
                    padding: 0.25rem 1.5rem 0.25rem 0.5rem;
                    font-size: 0.875rem;
                    border-radius: 0.375rem;
                    border: 1px solid #dee2e6;
                    background-color: #fff;
                    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                }

                .form-select-sm:focus {
                    border-color: #3498db;
                    outline: 0;
                    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
                }

                /* Pagination styling */
                .pagination {
                    margin-top: 2rem;
                }

                .page-link {
                    color: #3498db;
                    border: 1px solid #dee2e6;
                    padding: 0.5rem 0.75rem;
                    margin: 0 2px;
                    border-radius: 0.375rem;
                    transition: all 0.3s ease;
                    text-decoration: none;
                }

                .page-link:hover {
                    background-color: #3498db;
                    color: #fff;
                    border-color: #3498db;
                }

                .page-item.active .page-link {
                    background-color: #3498db;
                    border-color: #3498db;
                    color: #fff;
                }

                .page-item.disabled .page-link {
                    color: #6c757d;
                    background-color: #fff;
                    border-color: #dee2e6;
                }

                /* Responsive pagination controls */
                @media (max-width: 768px) {
                    .d-flex.justify-content-between {
                        flex-direction: column;
                        gap: 1rem;
                    }

                    .d-inline-flex.align-items-center {
                        justify-content: center;
                    }
                }
            </style>
            <div class="row">
                <?php $__empty_1 = true; $__currentLoopData = $achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <?php if($achievement->image): ?>
                            <img src="<?php echo e(asset('storage/prestasi/' . $achievement->image)); ?>" 
                                 class="card-img-top" 
                                 alt="<?php echo e($achievement->title); ?>">
                        <?php endif; ?>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo e($achievement->title); ?></h5>
                           <!-- <p class="text-muted">
                                <i class="fas fa-calendar"></i> 
                                <?php echo e($achievement->tanggal ? $achievement->tanggal->format('d M Y') : '-'); ?> 
                            </p> -->
                            <p class="text-muted">
                                <i class="fas fa-trophy"></i> 
                                <?php echo e($achievement->level); ?>

                            </p>
                            <p class="card-text"><?php echo e(Str::limit($achievement->description, 100)); ?></p>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        Belum ada data prestasi untuk jenjang <?php echo e(strtoupper($jenjang)); ?>.
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Pagination Controls -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <div class="mb-2">
                            <span class="text-muted">
                                <?php if(isset($perPage) && $perPage === 'all'): ?>
                                    Menampilkan semua <?php echo e($achievements->total()); ?> prestasi
                                <?php else: ?>
                                    Menampilkan <?php echo e($achievements->firstItem() ?? 0); ?> - <?php echo e($achievements->lastItem() ?? 0); ?> dari <?php echo e($achievements->total()); ?> prestasi
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="mb-2">
                            <form method="GET" action="<?php echo e(route('website.prestasi.jenjang', $jenjang)); ?>" class="d-inline-flex align-items-center">
                                <label for="per_page" class="me-2 text-muted">Tampilkan:</label>
                                <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                                    <option value="9" <?php echo e((isset($perPage) && $perPage == 9) ? 'selected' : ''); ?>>9</option>
                                    <option value="20" <?php echo e((isset($perPage) && $perPage == 20) ? 'selected' : ''); ?>>20</option>
                                    <option value="50" <?php echo e((isset($perPage) && $perPage == 50) ? 'selected' : ''); ?>>50</option>
                                    <option value="100" <?php echo e((isset($perPage) && $perPage == 100) ? 'selected' : ''); ?>>100</option>
                                    <option value="all" <?php echo e((isset($perPage) && $perPage === 'all') ? 'selected' : ''); ?>>Semua</option>
                                </select>
                                <span class="ms-2 text-muted">per halaman</span>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <?php if(isset($perPage) && $perPage !== 'all' && $achievements->hasPages()): ?>
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="Prestasi pagination">
                    <?php echo e($achievements->appends(['per_page' => $perPage])->links('pagination::bootstrap-4')); ?>

                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.website', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/website/prestasi/jenjang.blade.php ENDPATH**/ ?>