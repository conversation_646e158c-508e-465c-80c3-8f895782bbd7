<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('peserta_didik', function (Blueprint $table) {
            $table->string('NIK_ayah')->nullable()->after('nama_ayah');
            $table->string('NIK_ibu')->nullable()->after('nama_ibu');
            $table->string('NIK_wali')->nullable()->after('nama_wali');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('peserta_didik', function (Blueprint $table) {
            $table->dropColumn(['NIK_ayah', 'NIK_ibu', 'NIK_wali']);
        });
    }
};