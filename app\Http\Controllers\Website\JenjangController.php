<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Halaman;
use App\Models\Unit;
use App\Models\Article;
use App\Models\Event;
use App\Models\Achievement;
use App\Models\Extracurricular;
use Illuminate\Support\Facades\Log;

class JenjangController extends Controller
{
    private function getUnit($jenjang)
    {
        $jenjangId = strtoupper($jenjang === 'paud' ? 'pg' : $jenjang);
        return Unit::where('jenjang_id', $jenjangId)->firstOrFail();
    }

    public function showProfil($jenjang)
    {
        try {
            $unit = $this->getUnit($jenjang);
            $profil = Halaman::where('unit_id', $unit->id)
                ->where('tipe', 'profil')
                ->where('is_active', true)
                ->firstOrFail();

            return view('website.jenjang.profil', compact('profil', 'unit', 'jenjang'));
        } catch (\Exception $e) {
            Log::error('Error showing profile: ' . $e->getMessage());
            return $this->showError($jenjang, 'profil');
        }
    }

    public function showVisiMisi($jenjang)
    {
        try {
            $unit = $this->getUnit($jenjang);
            $visiMisi = Halaman::where('unit_id', $unit->id)
                ->where('tipe', 'visi-misi')
                ->where('is_active', true)
                ->firstOrFail();

            return view('website.jenjang.visi-misi', compact('visiMisi', 'unit', 'jenjang'));
        } catch (\Exception $e) {
            return $this->showError($jenjang, 'visi & misi');
        }
    }

    public function showSejarah($jenjang)
    {
        try {
            $unit = $this->getUnit($jenjang);
            $sejarah = Halaman::where('unit_id', $unit->id)
                ->where('tipe', 'sejarah')
                ->where('is_active', true)
                ->firstOrFail();

            return view('website.jenjang.sejarah', compact('sejarah', 'unit', 'jenjang'));
        } catch (\Exception $e) {
            return $this->showError($jenjang, 'sejarah');
        }
    }

    public function showArtikel($jenjang, Request $request)
    {
        try {
            $unit = $this->getUnit($jenjang);

            // Tambahkan debugging untuk melihat unit_id
            \Log::info('Unit found:', ['unit_id' => $unit->id, 'jenjang' => $jenjang]);

            $perPage = $request->get('per_page', 9);

            // Validasi nilai per_page
            $allowedPerPage = [9, 20, 50, 100, 'all'];
            if (!in_array($perPage, $allowedPerPage)) {
                $perPage = 9;
            }

            // Ambil artikel untuk unit spesifik dan artikel untuk semua unit (unit_id = null)
            $articlesQuery = Article::where(function($query) use ($unit) {
                    $query->where('unit_id', $unit->id)
                          ->orWhereNull('unit_id');
                })
                ->where('status', 'published')
                ->latest();

            // Tambahkan debugging untuk melihat query
            \Log::info('Query articles:', [
                'sql' => $articlesQuery->toSql(),
                'bindings' => $articlesQuery->getBindings()
            ]);

            // Jika 'all', ambil semua data tanpa pagination
            if ($perPage === 'all') {
                $articles = $articlesQuery->get();
                // Buat objek pagination manual untuk konsistensi
                $articles = new \Illuminate\Pagination\LengthAwarePaginator(
                    $articles,
                    $articles->count(),
                    $articles->count(),
                    1,
                    ['path' => request()->url(), 'pageName' => 'page']
                );
            } else {
                $articles = $articlesQuery->paginate($perPage);
                $articles->appends(['per_page' => $perPage]);
            }

            // Tambahkan debugging untuk melihat hasil
            \Log::info('Articles found:', ['count' => $articles->count()]);

            return view('website.jenjang.artikel', compact('articles', 'unit', 'jenjang', 'perPage'));
        } catch (\Exception $e) {
            \Log::error('Error in showArtikel: ' . $e->getMessage());
            return $this->showError($jenjang, 'artikel');
        }
    }

    public function showEvent($jenjang)
    {
        $unit = $this->getUnit($jenjang);
        
        // Ambil event untuk unit spesifik dan event untuk semua unit (unit_id = null)
        $events = Event::where(function($query) use ($unit) {
                $query->where('unit_id', $unit->id)
                      ->orWhereNull('unit_id');
            })
            ->latest()
            ->paginate(9);

        return view('website.jenjang.event', compact('events', 'unit', 'jenjang'));
    }

    public function showPrestasi($jenjang)
    {
        $unit = $this->getUnit($jenjang);
        $achievements = Achievement::where('unit_id', $unit->id)
            ->latest()
            ->paginate(10);

        return view('website.jenjang.prestasi', compact('achievements', 'unit', 'jenjang'));
    }

    public function showEkstrakurikuler($jenjang)
    {
        $unit = $this->getUnit($jenjang);
        $ekstrakurikuler = Extracurricular::where('unit_id', $unit->id)
            ->get();

        return view('website.jenjang.ekstrakurikuler', compact('ekstrakurikuler', 'unit', 'jenjang'));
    }

    private function showError($jenjang, $page)
    {
        return response()->view('errors.404', [
            'message' => ucfirst($page) . ' untuk jenjang ' . strtoupper($jenjang) . ' belum tersedia.',
            'back_url' => url()->previous(),
            'jenjang' => $jenjang
        ], 404);
    }
}
