<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('achievements', function (Blueprint $table) {
            $table->id();
            $table->string('title');  // sesuai dengan struktur di database dump
            $table->text('description')->nullable();  // sesuai dengan struktur di database dump
            $table->string('level');  // sesuai dengan struktur di database dump
            $table->string('participant');  // sesuai dengan struktur di database dump
            $table->string('image')->nullable();  // sesuai dengan struktur di database dump
            $table->foreignId('unit_id')->nullable()->constrained('units')->onDelete('set null');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('achievements');
    }
};
