<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('peserta_didik', function (Blueprint $table) {
            $table->enum('status', ['aktif', 'alumni', 'mutasi_keluar', 'tidak_aktif'])
                  ->default('aktif')
                  ->after('kelas_id');
        });
    }

    public function down()
    {
        Schema::table('peserta_didik', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};